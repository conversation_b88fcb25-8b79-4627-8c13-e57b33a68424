{"lastValidatedTimestamp": 1750430642433, "projects": {"C:\\Users\\<USER>\\Desktop\\websites\\New folder\\public-link": {"name": "public-link"}, "C:\\Users\\<USER>\\Desktop\\websites\\New folder\\public-link\\apps\\api": {"name": "api", "version": "0.1.0"}, "C:\\Users\\<USER>\\Desktop\\websites\\New folder\\public-link\\apps\\dashboard": {"name": "dashboard", "version": "0.0.0"}, "C:\\Users\\<USER>\\Desktop\\websites\\New folder\\public-link\\apps\\worker": {"name": "worker", "version": "0.1.0"}}, "pnpmfileExists": false, "settings": {"autoInstallPeers": true, "catalogs": {}, "dedupeDirectDeps": false, "dedupeInjectedDeps": true, "dedupePeerDependents": true, "dev": true, "excludeLinksFromLockfile": false, "hoistPattern": ["*"], "hoistWorkspacePackages": true, "injectWorkspacePackages": false, "linkWorkspacePackages": false, "nodeLinker": "isolated", "optional": true, "preferWorkspacePackages": false, "production": true, "publicHoistPattern": [], "workspacePackagePatterns": ["apps/*"]}, "filteredInstall": true}