import { Octokit } from '@octokit/rest';

// GitHub token from environment variable
const GITHUB_TOKEN = process.env.GITHUB_TOKEN || 'your-github-token-here';

export async function deployUrlDirectly(url, jobStatuses, jobId) {
  const octokit = new Octokit({ auth: GITHUB_TOKEN });
  
  try {
    // Update progress
    jobStatuses[jobId] = { status: 'active', progress: 10 };
    
    console.log(`Fetching ${url}...`);
    const response = await fetch(url);
    
    if (!response.ok) {
      throw new Error(`Failed to fetch ${url}: ${response.statusText}`);
    }
    
    jobStatuses[jobId] = { status: 'active', progress: 25 };
    
    let html = await response.text();

    // Add static deployment notice
    const staticNotice = `
    <div style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 10px; margin: 10px; border-radius: 5px; font-family: Arial, sans-serif;">
      <strong>📌 Static Deployment Notice:</strong> This is a static version deployed via Public-Link.
      Backend functionality (APIs, server calls) is not available. Only frontend/client-side features work.
    </div>`;

    // Insert notice after body tag
    html = html.replace(/<body([^>]*)>/i, `<body$1>${staticNotice}`);

    // Extract CSS and JS files from HTML
    const cssMatches = html.match(/<link[^>]*href=["']([^"']*\.css[^"']*?)["'][^>]*>/gi) || [];
    const jsMatches = html.match(/<script[^>]*src=["']([^"']*\.js[^"']*?)["'][^>]*>/gi) || [];
    
    jobStatuses[jobId] = { status: 'active', progress: 40 };
    
    // Create repository
    const repoName = `public-link-${Date.now()}`;
    console.log(`Creating GitHub repository: ${repoName}`);
    
    const repo = await octokit.repos.createForAuthenticatedUser({
      name: repoName,
      description: `Public deployment of ${url}`,
      public: true,
      auto_init: false
    });
    
    console.log(`Repository created: ${repo.data.html_url}`);
    jobStatuses[jobId] = { status: 'active', progress: 55 };
    
    // Upload main HTML file
    await octokit.repos.createOrUpdateFileContents({
      owner: repo.data.owner.login,
      repo: repo.data.name,
      path: 'index.html',
      message: 'Deploy main HTML file',
      content: Buffer.from(html).toString('base64')
    });
    
    jobStatuses[jobId] = { status: 'active', progress: 70 };
    
    // Fetch and upload CSS files
    for (const cssMatch of cssMatches) {
      try {
        const hrefMatch = cssMatch.match(/href=["']([^"']*?)["']/);
        if (hrefMatch) {
          const cssUrl = new URL(hrefMatch[1], url).href;
          const cssResponse = await fetch(cssUrl);
          if (cssResponse.ok) {
            const cssContent = await cssResponse.text();
            const originalPath = hrefMatch[1];
            const fileName = originalPath.startsWith('/') ? originalPath.substring(1) : originalPath;

            await octokit.repos.createOrUpdateFileContents({
              owner: repo.data.owner.login,
              repo: repo.data.name,
              path: fileName,
              message: `Deploy CSS file: ${fileName}`,
              content: Buffer.from(cssContent).toString('base64')
            });
          }
        }
      } catch (error) {
        console.log(`Failed to fetch CSS file: ${error.message}`);
      }
    }
    
    // Fetch and upload JS files
    for (const jsMatch of jsMatches) {
      try {
        const srcMatch = jsMatch.match(/src=["']([^"']*?)["']/);
        if (srcMatch) {
          const jsUrl = new URL(srcMatch[1], url).href;
          const jsResponse = await fetch(jsUrl);
          if (jsResponse.ok) {
            let jsContent = await jsResponse.text();
            const originalPath = srcMatch[1];
            const fileName = originalPath.startsWith('/') ? originalPath.substring(1) : originalPath;

            // Fix API calls in JavaScript - replace localhost API calls with static behavior
            jsContent = jsContent.replace(
              /fetch\s*\(\s*['"`][^'"`]*localhost[^'"`]*['"`]/g,
              'fetch("#"'
            );

            // Add static fallback for common API patterns
            if (jsContent.includes('fetch(') && jsContent.includes('localhost')) {
              jsContent = `
// Static fallback for GitHub Pages deployment
const originalFetch = window.fetch;
window.fetch = function(url, options) {
  if (typeof url === 'string' && (url.includes('localhost') || url.startsWith('/api'))) {
    console.warn('API call blocked in static deployment:', url);
    return Promise.resolve({
      ok: false,
      status: 405,
      statusText: 'Method Not Allowed - Static deployment',
      json: () => Promise.resolve({ error: 'Backend not available in static deployment' }),
      text: () => Promise.resolve('Backend not available in static deployment')
    });
  }
  return originalFetch.apply(this, arguments);
};

${jsContent}`;
            }

            await octokit.repos.createOrUpdateFileContents({
              owner: repo.data.owner.login,
              repo: repo.data.name,
              path: fileName,
              message: `Deploy JS file: ${fileName}`,
              content: Buffer.from(jsContent).toString('base64')
            });
          }
        }
      } catch (error) {
        console.log(`Failed to fetch JS file: ${error.message}`);
      }
    }
    
    jobStatuses[jobId] = { status: 'active', progress: 85 };
    
    // Enable GitHub Pages
    console.log(`Enabling GitHub Pages for ${repoName}...`);
    let pagesEnabled = false;
    
    try {
      await octokit.repos.createPagesSite({
        owner: repo.data.owner.login,
        repo: repo.data.name,
        source: { branch: 'main', path: '/' }
      });
      pagesEnabled = true;
      console.log('GitHub Pages enabled successfully');
    } catch (error) {
      console.log('GitHub Pages enable failed:', error.message);
    }
    
    const githubPagesUrl = `https://${repo.data.owner.login}.github.io/${repo.data.name}`;
    
    jobStatuses[jobId] = { status: 'active', progress: 100 };
    
    console.log('\n🎉 Deployment successful!');
    console.log(`📁 Repository: ${repo.data.html_url}`);
    console.log(`🌐 Public URL: ${githubPagesUrl}`);
    console.log('⏰ GitHub Pages will be live in 2-3 minutes');
    
    return {
      message: pagesEnabled
        ? 'Successfully deployed to GitHub! GitHub Pages is being built (may take 2-3 minutes to be live).'
        : 'Successfully deployed to GitHub! Please manually enable GitHub Pages in repository settings.',
      githubRepo: repo.data.html_url,
      githubPages: githubPagesUrl,
      originalUrl: url,
      repoName: repoName,
      pagesEnabled: pagesEnabled,
      note: pagesEnabled
        ? 'GitHub Pages URL will be live in 2-3 minutes'
        : 'GitHub Pages needs to be manually enabled'
    };
    
  } catch (error) {
    console.error(`Failed to deploy URL ${url}:`, error);
    throw error;
  }
}
