import { Octokit } from '@octokit/rest';

// Hardcoded GitHub token for testing
const GITHUB_TOKEN = '****************************************';

export async function deployUrlDirectly(url, jobStatuses, jobId) {
  const octokit = new Octokit({ auth: GITHUB_TOKEN });
  
  try {
    // Update progress
    jobStatuses[jobId] = { status: 'active', progress: 10 };
    
    console.log(`Fetching ${url}...`);
    const response = await fetch(url);
    
    if (!response.ok) {
      throw new Error(`Failed to fetch ${url}: ${response.statusText}`);
    }
    
    jobStatuses[jobId] = { status: 'active', progress: 25 };
    
    const html = await response.text();
    
    // Extract CSS and JS files from HTML
    const cssMatches = html.match(/<link[^>]*href=["']([^"']*\.css[^"']*?)["'][^>]*>/gi) || [];
    const jsMatches = html.match(/<script[^>]*src=["']([^"']*\.js[^"']*?)["'][^>]*>/gi) || [];
    
    jobStatuses[jobId] = { status: 'active', progress: 40 };
    
    // Create repository
    const repoName = `public-link-${Date.now()}`;
    console.log(`Creating GitHub repository: ${repoName}`);
    
    const repo = await octokit.repos.createForAuthenticatedUser({
      name: repoName,
      description: `Public deployment of ${url}`,
      public: true,
      auto_init: false
    });
    
    console.log(`Repository created: ${repo.data.html_url}`);
    jobStatuses[jobId] = { status: 'active', progress: 55 };
    
    // Upload main HTML file
    await octokit.repos.createOrUpdateFileContents({
      owner: repo.data.owner.login,
      repo: repo.data.name,
      path: 'index.html',
      message: 'Deploy main HTML file',
      content: Buffer.from(html).toString('base64')
    });
    
    jobStatuses[jobId] = { status: 'active', progress: 70 };
    
    // Fetch and upload CSS files
    for (const cssMatch of cssMatches) {
      try {
        const hrefMatch = cssMatch.match(/href=["']([^"']*?)["']/);
        if (hrefMatch) {
          const cssUrl = new URL(hrefMatch[1], url).href;
          const cssResponse = await fetch(cssUrl);
          if (cssResponse.ok) {
            const cssContent = await cssResponse.text();
            const fileName = cssUrl.split('/').pop() || 'style.css';
            
            await octokit.repos.createOrUpdateFileContents({
              owner: repo.data.owner.login,
              repo: repo.data.name,
              path: fileName,
              message: `Deploy CSS file: ${fileName}`,
              content: Buffer.from(cssContent).toString('base64')
            });
          }
        }
      } catch (error) {
        console.log(`Failed to fetch CSS file: ${error.message}`);
      }
    }
    
    // Fetch and upload JS files
    for (const jsMatch of jsMatches) {
      try {
        const srcMatch = jsMatch.match(/src=["']([^"']*?)["']/);
        if (srcMatch) {
          const jsUrl = new URL(srcMatch[1], url).href;
          const jsResponse = await fetch(jsUrl);
          if (jsResponse.ok) {
            const jsContent = await jsResponse.text();
            const fileName = jsUrl.split('/').pop() || 'script.js';
            
            await octokit.repos.createOrUpdateFileContents({
              owner: repo.data.owner.login,
              repo: repo.data.name,
              path: fileName,
              message: `Deploy JS file: ${fileName}`,
              content: Buffer.from(jsContent).toString('base64')
            });
          }
        }
      } catch (error) {
        console.log(`Failed to fetch JS file: ${error.message}`);
      }
    }
    
    jobStatuses[jobId] = { status: 'active', progress: 85 };
    
    // Enable GitHub Pages
    console.log(`Enabling GitHub Pages for ${repoName}...`);
    let pagesEnabled = false;
    
    try {
      await octokit.repos.createPagesSite({
        owner: repo.data.owner.login,
        repo: repo.data.name,
        source: { branch: 'main', path: '/' }
      });
      pagesEnabled = true;
      console.log('GitHub Pages enabled successfully');
    } catch (error) {
      console.log('GitHub Pages enable failed:', error.message);
    }
    
    const githubPagesUrl = `https://${repo.data.owner.login}.github.io/${repo.data.name}`;
    
    jobStatuses[jobId] = { status: 'active', progress: 100 };
    
    console.log('\n🎉 Deployment successful!');
    console.log(`📁 Repository: ${repo.data.html_url}`);
    console.log(`🌐 Public URL: ${githubPagesUrl}`);
    console.log('⏰ GitHub Pages will be live in 2-3 minutes');
    
    return {
      message: pagesEnabled
        ? 'Successfully deployed to GitHub! GitHub Pages is being built (may take 2-3 minutes to be live).'
        : 'Successfully deployed to GitHub! Please manually enable GitHub Pages in repository settings.',
      githubRepo: repo.data.html_url,
      githubPages: githubPagesUrl,
      originalUrl: url,
      repoName: repoName,
      pagesEnabled: pagesEnabled,
      note: pagesEnabled
        ? 'GitHub Pages URL will be live in 2-3 minutes'
        : 'GitHub Pages needs to be manually enabled'
    };
    
  } catch (error) {
    console.error(`Failed to deploy URL ${url}:`, error);
    throw error;
  }
}
