{"name": "worker", "version": "0.1.0", "main": "dist/index.js", "type": "module", "scripts": {"dev": "nodemon --exec tsx src/index.ts", "build": "tsc"}, "dependencies": {"@octokit/rest": "^20.0.0", "adm-zip": "^0.5.10", "bullmq": "^4.0.3", "dotenv": "^16.4.0", "execa": "^8.0.1", "ioredis": "^5.3.2", "tmp-promise": "^3.0.3"}, "devDependencies": {"nodemon": "^3.0.3", "ts-node": "^10.9.2", "tsx": "^4.20.3", "typescript": "^5.5.0"}}