import 'dotenv/config';
import { QueueE<PERSON>s, Worker } from 'bullmq';
import Redis from 'ioredis';
import { Octokit } from '@octokit/rest';
import AdmZip from 'adm-zip';
import tmp from 'tmp-promise';
import { execa } from 'execa';
import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';
import { createWriteStream } from 'fs';

const redis = new Redis(process.env.REDIS_URL ?? 'redis://localhost:6379');

// Temporary hardcoded token for testing
const GITHUB_TOKEN = '****************************************';

console.log('GitHub PAT loaded:', process.env.GITHUB_PAT ? 'Yes (length: ' + process.env.GITHUB_PAT.length + ')' : 'No');
console.log('Using hardcoded token for testing');

const octokit = new Octokit({ auth: GITHUB_TOKEN });

// Helper function to scrape localhost website
async function scrapeLocalhost(url: string): Promise<string> {
  const tmpDir = await tmp.dir();

  try {
    console.log(`Fetching ${url}...`);
    const response = await fetch(url);

    if (!response.ok) {
      throw new Error(`Failed to fetch ${url}: ${response.statusText}`);
    }

    const html = await response.text();

    // Create a basic HTML file
    const indexPath = path.join(tmpDir.path, 'index.html');
    await fs.writeFile(indexPath, html);

    // Try to extract and download CSS/JS references
    const cssMatches = html.match(/href=["']([^"']*\.css)["']/g) || [];
    const jsMatches = html.match(/src=["']([^"']*\.js)["']/g) || [];

    const baseUrl = new URL(url);

    // Download CSS files
    for (const match of cssMatches) {
      try {
        const cssUrl = match.match(/href=["']([^"']*)["']/)?.[1];
        if (cssUrl && !cssUrl.startsWith('http')) {
          const fullCssUrl = new URL(cssUrl, baseUrl).href;
          const cssResponse = await fetch(fullCssUrl);
          if (cssResponse.ok) {
            const cssContent = await cssResponse.text();
            const cssPath = path.join(tmpDir.path, path.basename(cssUrl));
            await fs.writeFile(cssPath, cssContent);
          }
        }
      } catch (e) {
        console.log(`Failed to download CSS: ${e}`);
      }
    }

    // Download JS files
    for (const match of jsMatches) {
      try {
        const jsUrl = match.match(/src=["']([^"']*)["']/)?.[1];
        if (jsUrl && !jsUrl.startsWith('http')) {
          const fullJsUrl = new URL(jsUrl, baseUrl).href;
          const jsResponse = await fetch(fullJsUrl);
          if (jsResponse.ok) {
            const jsContent = await jsResponse.text();
            const jsPath = path.join(tmpDir.path, path.basename(jsUrl));
            await fs.writeFile(jsPath, jsContent);
          }
        }
      } catch (e) {
        console.log(`Failed to download JS: ${e}`);
      }
    }

    return tmpDir.path;
  } catch (error) {
    console.error('Failed to scrape localhost:', error);
    throw error;
  }
}

const deployWorker = new Worker(
  'deploy',
  async job => {
    const jobData = job.data as { path?: string; url?: string };
    let tmpDir: tmp.DirectoryResult;

    if (jobData.path) {
      // Original zip file deployment
      tmpDir = await tmp.dir();
      new AdmZip(jobData.path).extractAllTo(tmpDir.path, true);
    } else if (jobData.url) {
      // New localhost URL deployment
      const scrapedPath = await scrapeLocalhost(jobData.url);
      tmpDir = { path: scrapedPath } as tmp.DirectoryResult;
    } else {
      throw new Error('Either path or url must be provided');
    }

    const repoName = `public-link-${Date.now()}`;
    const { data: repo } = await octokit.repos.createForAuthenticatedUser({
      name: repoName,
      private: true
    });

    await execa('git', ['init'], { cwd: tmpDir.path });
    await execa('git', ['checkout', '-b', 'main'], { cwd: tmpDir.path });
    await execa('git', ['add', '.'], { cwd: tmpDir.path });
    await execa('git', ['commit', '-m', 'initial commit'], { cwd: tmpDir.path });
    const remote = repo.clone_url.replace('https://', `https://${GITHUB_TOKEN}@`);
    await execa('git', ['push', '--force', remote, 'main'], { cwd: tmpDir.path });

    return { repo: repo.html_url };
  },
  { connection: redis }
);

// Worker for URL-based deployments
const deployUrlWorker = new Worker(
  'deploy-url',
  async job => {
    const { url } = job.data as { url: string };
    console.log(`Processing URL deployment for: ${url}`);

    try {
      const scrapedPath = await scrapeLocalhost(url);
      console.log(`Successfully scraped content to: ${scrapedPath}`);

      const tmpDir = { path: scrapedPath } as tmp.DirectoryResult;

      const repoName = `public-link-${Date.now()}`;
      console.log(`Creating GitHub repository: ${repoName}`);

      const { data: repo } = await octokit.repos.createForAuthenticatedUser({
        name: repoName,
        private: false, // Make it public for GitHub Pages
        description: `Public link deployment from ${url}`,
        auto_init: false
      });

      console.log(`Repository created: ${repo.html_url}`);

      // Initialize git and push to GitHub
      await execa('git', ['init'], { cwd: tmpDir.path });
      await execa('git', ['config', 'user.email', '<EMAIL>'], { cwd: tmpDir.path });
      await execa('git', ['config', 'user.name', 'Public Link Bot'], { cwd: tmpDir.path });
      await execa('git', ['checkout', '-b', 'main'], { cwd: tmpDir.path });
      await execa('git', ['add', '.'], { cwd: tmpDir.path });
      await execa('git', ['commit', '-m', 'Initial deployment from Public-Link'], { cwd: tmpDir.path });

      const remote = repo.clone_url.replace('https://', `https://${GITHUB_TOKEN}@`);
      await execa('git', ['remote', 'add', 'origin', remote], { cwd: tmpDir.path });
      await execa('git', ['push', '-u', 'origin', 'main'], { cwd: tmpDir.path });

      console.log(`Code pushed to GitHub successfully`);

      // Enable GitHub Pages
      try {
        console.log(`Enabling GitHub Pages for ${repoName}...`);
        await octokit.repos.createPagesSite({
          owner: repo.owner.login,
          repo: repo.name,
          source: {
            branch: 'main',
            path: '/'
          }
        });
        console.log(`GitHub Pages enabled successfully`);
      } catch (pagesError) {
        console.log(`GitHub Pages setup failed (might already be enabled):`, pagesError);
      }

      return {
        message: 'Successfully deployed to GitHub!',
        githubRepo: repo.html_url,
        githubPages: `https://${repo.owner.login}.github.io/${repo.name}`,
        originalUrl: url,
        repoName: repoName
      };
    } catch (error) {
      console.error(`Failed to deploy URL ${url}:`, error);
      throw error;
    }
  },
  { connection: redis }
);

new QueueEvents('deploy', { connection: redis });
new QueueEvents('deploy-url', { connection: redis });
console.log('Worker ready…');