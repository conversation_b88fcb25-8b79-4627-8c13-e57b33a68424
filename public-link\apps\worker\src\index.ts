import 'dotenv/config';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Worker } from 'bullmq';
import Redis from 'ioredis';
import { Octokit } from '@octokit/rest';
import AdmZip from 'adm-zip';
import tmp from 'tmp-promise';
import { execa } from 'execa';

const redis = new Redis(process.env.REDIS_URL ?? 'redis://localhost:6379');
const octokit = new Octokit({ auth: process.env.GITHUB_PAT });

const deployWorker = new Worker(
  'deploy',
  async job => {
    const { path: zipPath } = job.data as { path: string };
    const tmpDir = await tmp.dir();
    new AdmZip(zipPath).extractAllTo(tmpDir.path, true);

    const repoName = `public-link-${Date.now()}`;
    const { data: repo } = await octokit.repos.createForAuthenticatedUser({
      name: repoName,
      private: true
    });

    await execa('git', ['init'], { cwd: tmpDir.path });
    await execa('git', ['checkout', '-b', 'main'], { cwd: tmpDir.path });
    await execa('git', ['add', '.'], { cwd: tmpDir.path });
    await execa('git', ['commit', '-m', 'initial commit'], { cwd: tmpDir.path });
    const remote = repo.clone_url.replace('https://', `https://${process.env.GITHUB_PAT}@`);
    await execa('git', ['push', '--force', remote, 'main'], { cwd: tmpDir.path });

    return { repo: repo.html_url };
  },
  { connection: redis }
);

new QueueEvents('deploy', { connection: redis });
console.log('Worker ready…'); 