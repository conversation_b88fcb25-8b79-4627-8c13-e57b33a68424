import 'dotenv/config';
import { QueueEvents, Worker } from 'bullmq';
import Redis from 'ioredis';
import { Octokit } from '@octokit/rest';
import AdmZip from 'adm-zip';
import tmp from 'tmp-promise';
import { execa } from 'execa';
import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';
import { createWriteStream } from 'fs';

// Deployment platform types
type DeploymentPlatform = 'github-pages' | 'vercel' | 'netlify' | 'railway' | 'heroku';

interface DeploymentConfig {
  platform: DeploymentPlatform;
  apiKey?: string;
  projectName?: string;
  buildCommand?: string;
  outputDir?: string;
}

const redis = new Redis(process.env.REDIS_URL ?? 'redis://localhost:6379');

// Temporary hardcoded token for testing
const GITHUB_TOKEN = process.env.GITHUB_TOKEN || 'your-github-token-here';

console.log('GitHub PAT loaded:', process.env.GITHUB_PAT ? 'Yes (length: ' + process.env.GITHUB_PAT.length + ')' : 'No');
console.log('Using hardcoded token for testing');

const octokit = new Octokit({ auth: GITHUB_TOKEN });

// Platform detection based on application structure
async function detectApplicationType(dirPath: string): Promise<{
  type: 'static' | 'node' | 'react' | 'vue' | 'angular' | 'next' | 'express';
  hasBackend: boolean;
  buildCommand?: string;
  startCommand?: string;
  outputDir?: string;
}> {
  try {
    const packageJsonPath = path.join(dirPath, 'package.json');
    const packageJsonExists = await fs.access(packageJsonPath).then(() => true).catch(() => false);

    if (packageJsonExists) {
      const packageJson = JSON.parse(await fs.readFile(packageJsonPath, 'utf-8'));
      const dependencies = { ...packageJson.dependencies, ...packageJson.devDependencies };

      // Detect framework/type
      if (dependencies.next) {
        return {
          type: 'next',
          hasBackend: true,
          buildCommand: 'npm run build',
          startCommand: 'npm start',
          outputDir: '.next'
        };
      } else if (dependencies.react) {
        return {
          type: 'react',
          hasBackend: false,
          buildCommand: 'npm run build',
          outputDir: 'build'
        };
      } else if (dependencies.vue) {
        return {
          type: 'vue',
          hasBackend: false,
          buildCommand: 'npm run build',
          outputDir: 'dist'
        };
      } else if (dependencies['@angular/core']) {
        return {
          type: 'angular',
          hasBackend: false,
          buildCommand: 'npm run build',
          outputDir: 'dist'
        };
      } else if (dependencies.express || dependencies.fastify || dependencies.koa) {
        return {
          type: 'express',
          hasBackend: true,
          startCommand: packageJson.scripts?.start || 'node index.js'
        };
      } else if (packageJson.scripts?.start) {
        return {
          type: 'node',
          hasBackend: true,
          startCommand: packageJson.scripts.start
        };
      }
    }

    // Check for static files
    const indexHtml = await fs.access(path.join(dirPath, 'index.html')).then(() => true).catch(() => false);
    if (indexHtml) {
      return {
        type: 'static',
        hasBackend: false
      };
    }

    return {
      type: 'static',
      hasBackend: false
    };
  } catch (error) {
    console.error('Error detecting application type:', error);
    return {
      type: 'static',
      hasBackend: false
    };
  }
}

// Deploy to Vercel
async function deployToVercel(dirPath: string, appInfo: any): Promise<any> {
  try {
    console.log('Deploying to Vercel...');

    // Install Vercel CLI if not present
    try {
      await execa('vercel', ['--version']);
    } catch {
      console.log('Installing Vercel CLI...');
      await execa('npm', ['install', '-g', 'vercel']);
    }

    // Deploy with Vercel
    const result = await execa('vercel', ['--prod', '--yes', '--token', process.env.VERCEL_TOKEN || ''], {
      cwd: dirPath,
      env: { ...process.env, VERCEL_ORG_ID: process.env.VERCEL_ORG_ID, VERCEL_PROJECT_ID: process.env.VERCEL_PROJECT_ID }
    });

    const deploymentUrl = result.stdout.split('\n').find(line => line.includes('https://'))?.trim();

    return {
      platform: 'vercel',
      url: deploymentUrl,
      message: 'Successfully deployed to Vercel with full backend support!'
    };
  } catch (error) {
    console.error('Vercel deployment failed:', error);
    throw new Error(`Vercel deployment failed: ${error}`);
  }
}

// Deploy to Railway
async function deployToRailway(dirPath: string, appInfo: any): Promise<any> {
  try {
    console.log('Deploying to Railway...');

    // Create railway.json config
    const railwayConfig = {
      build: {
        builder: 'NIXPACKS'
      },
      deploy: {
        startCommand: appInfo.startCommand || 'npm start',
        healthcheckPath: '/',
        healthcheckTimeout: 100,
        restartPolicyType: 'ON_FAILURE'
      }
    };

    await fs.writeFile(path.join(dirPath, 'railway.json'), JSON.stringify(railwayConfig, null, 2));

    // Install Railway CLI if not present
    try {
      await execa('railway', ['--version']);
    } catch {
      console.log('Installing Railway CLI...');
      await execa('npm', ['install', '-g', '@railway/cli']);
    }

    // Login and deploy
    await execa('railway', ['login', '--token', process.env.RAILWAY_TOKEN || ''], { cwd: dirPath });
    const result = await execa('railway', ['up', '--detach'], { cwd: dirPath });

    // Get deployment URL
    const statusResult = await execa('railway', ['status'], { cwd: dirPath });
    const deploymentUrl = statusResult.stdout.match(/https:\/\/[^\s]+/)?.[0];

    return {
      platform: 'railway',
      url: deploymentUrl,
      message: 'Successfully deployed to Railway with full backend support!'
    };
  } catch (error) {
    console.error('Railway deployment failed:', error);
    throw new Error(`Railway deployment failed: ${error}`);
  }
}

// Helper function to scrape localhost website
async function scrapeLocalhost(url: string): Promise<string> {
  const tmpDir = await tmp.dir();

  try {
    console.log(`Fetching ${url}...`);
    const response = await fetch(url);

    if (!response.ok) {
      throw new Error(`Failed to fetch ${url}: ${response.statusText}`);
    }

    const html = await response.text();

    // Create a basic HTML file
    const indexPath = path.join(tmpDir.path, 'index.html');
    await fs.writeFile(indexPath, html);

    // Try to extract and download CSS/JS references
    const cssMatches = html.match(/href=["']([^"']*\.css)["']/g) || [];
    const jsMatches = html.match(/src=["']([^"']*\.js)["']/g) || [];

    const baseUrl = new URL(url);

    // Download CSS files
    for (const match of cssMatches) {
      try {
        const cssUrl = match.match(/href=["']([^"']*)["']/)?.[1];
        if (cssUrl && !cssUrl.startsWith('http')) {
          const fullCssUrl = new URL(cssUrl, baseUrl).href;
          const cssResponse = await fetch(fullCssUrl);
          if (cssResponse.ok) {
            const cssContent = await cssResponse.text();
            const cssPath = path.join(tmpDir.path, path.basename(cssUrl));
            await fs.writeFile(cssPath, cssContent);
          }
        }
      } catch (e) {
        console.log(`Failed to download CSS: ${e}`);
      }
    }

    // Download JS files
    for (const match of jsMatches) {
      try {
        const jsUrl = match.match(/src=["']([^"']*)["']/)?.[1];
        if (jsUrl && !jsUrl.startsWith('http')) {
          const fullJsUrl = new URL(jsUrl, baseUrl).href;
          const jsResponse = await fetch(fullJsUrl);
          if (jsResponse.ok) {
            const jsContent = await jsResponse.text();
            const jsPath = path.join(tmpDir.path, path.basename(jsUrl));
            await fs.writeFile(jsPath, jsContent);
          }
        }
      } catch (e) {
        console.log(`Failed to download JS: ${e}`);
      }
    }

    return tmpDir.path;
  } catch (error) {
    console.error('Failed to scrape localhost:', error);
    throw error;
  }
}

const deployWorker = new Worker(
  'deploy',
  async job => {
    const jobData = job.data as { path?: string; url?: string };
    let tmpDir: tmp.DirectoryResult;

    if (jobData.path) {
      // Original zip file deployment
      tmpDir = await tmp.dir();
      new AdmZip(jobData.path).extractAllTo(tmpDir.path, true);
    } else if (jobData.url) {
      // New localhost URL deployment
      const scrapedPath = await scrapeLocalhost(jobData.url);
      tmpDir = { path: scrapedPath } as tmp.DirectoryResult;
    } else {
      throw new Error('Either path or url must be provided');
    }

    const repoName = `public-link-${Date.now()}`;
    const { data: repo } = await octokit.repos.createForAuthenticatedUser({
      name: repoName,
      private: true
    });

    await execa('git', ['init'], { cwd: tmpDir.path });
    await execa('git', ['checkout', '-b', 'main'], { cwd: tmpDir.path });
    await execa('git', ['add', '.'], { cwd: tmpDir.path });
    await execa('git', ['commit', '-m', 'initial commit'], { cwd: tmpDir.path });
    const remote = repo.clone_url.replace('https://', `https://${GITHUB_TOKEN}@`);
    await execa('git', ['push', '--force', remote, 'main'], { cwd: tmpDir.path });

    return { repo: repo.html_url };
  },
  { connection: redis }
);

// Worker for URL-based deployments with platform selection
const deployUrlWorker = new Worker(
  'deploy-url',
  async job => {
    const { url, platform = 'auto' } = job.data as { url: string; platform?: string };
    console.log(`Processing URL deployment for: ${url} on platform: ${platform}`);

    try {
      const scrapedPath = await scrapeLocalhost(url);
      console.log(`Successfully scraped content to: ${scrapedPath}`);

      const tmpDir = { path: scrapedPath } as tmp.DirectoryResult;

      // Detect application type
      const appInfo = await detectApplicationType(scrapedPath);
      console.log(`Detected application type:`, appInfo);

      // Choose deployment platform based on app type and user preference
      let targetPlatform = platform;
      if (platform === 'auto') {
        if (appInfo.hasBackend) {
          // For backend apps, prefer platforms that support server-side code
          targetPlatform = process.env.RAILWAY_TOKEN ? 'railway' :
                          process.env.VERCEL_TOKEN ? 'vercel' : 'github-pages';
        } else {
          // For static apps, GitHub Pages is fine
          targetPlatform = 'github-pages';
        }
      }

      console.log(`Selected deployment platform: ${targetPlatform}`);

      // Deploy based on selected platform
      if (targetPlatform === 'vercel' && appInfo.hasBackend) {
        return await deployToVercel(scrapedPath, appInfo);
      } else if (targetPlatform === 'railway' && appInfo.hasBackend) {
        return await deployToRailway(scrapedPath, appInfo);
      } else {
        // Fallback to GitHub Pages for static content
        return await deployToGitHubPages(scrapedPath, url, appInfo);
      }
    } catch (error) {
      console.error(`Failed to deploy URL ${url}:`, error);
      throw error;
    }
  },
  { connection: redis }
);

// GitHub Pages deployment function (extracted from original logic)
async function deployToGitHubPages(scrapedPath: string, originalUrl: string, appInfo: any) {
  const tmpDir = { path: scrapedPath } as tmp.DirectoryResult;

  const repoName = `public-link-${Date.now()}`;
  console.log(`Creating GitHub repository: ${repoName}`);

  const { data: repo } = await octokit.repos.createForAuthenticatedUser({
    name: repoName,
    private: false, // Make it public for GitHub Pages
    description: `Public link deployment from ${originalUrl}`,
    auto_init: false
  });

  console.log(`Repository created: ${repo.html_url}`);

  // Initialize git and push to GitHub
  await execa('git', ['init'], { cwd: tmpDir.path });
  await execa('git', ['config', 'user.email', '<EMAIL>'], { cwd: tmpDir.path });
  await execa('git', ['config', 'user.name', 'Public Link Bot'], { cwd: tmpDir.path });
  await execa('git', ['checkout', '-b', 'main'], { cwd: tmpDir.path });
  await execa('git', ['add', '.'], { cwd: tmpDir.path });
  await execa('git', ['commit', '-m', 'Initial deployment from Public-Link'], { cwd: tmpDir.path });

  const remote = repo.clone_url.replace('https://', `https://${GITHUB_TOKEN}@`);
  await execa('git', ['remote', 'add', 'origin', remote], { cwd: tmpDir.path });
  await execa('git', ['push', '-u', 'origin', 'main'], { cwd: tmpDir.path });

  console.log(`Code pushed to GitHub successfully`);

  // Enable GitHub Pages
  let githubPagesUrl = `https://${repo.owner.login}.github.io/${repo.name}`;
  let pagesEnabled = false;

  try {
    console.log(`Enabling GitHub Pages for ${repoName}...`);
    const pagesResponse = await octokit.repos.createPagesSite({
      owner: repo.owner.login,
      repo: repo.name,
      source: {
        branch: 'main',
        path: '/'
      }
    });
    console.log(`GitHub Pages enabled successfully`);
    githubPagesUrl = pagesResponse.data.html_url || githubPagesUrl;
    pagesEnabled = true;
  } catch (pagesError: any) {
    console.log(`GitHub Pages setup failed:`, pagesError.message);
    // If it fails, still return the expected URL but with a note
  }

  const warningMessage = appInfo.hasBackend
    ? ' ⚠️ Warning: This application has backend functionality that will not work on GitHub Pages (static hosting only).'
    : '';

  return {
    platform: 'github-pages',
    message: (pagesEnabled
      ? 'Successfully deployed to GitHub Pages! Site is being built (may take 2-3 minutes to be live).'
      : 'Successfully deployed to GitHub! Please manually enable GitHub Pages in repository settings.') + warningMessage,
    githubRepo: repo.html_url,
    githubPages: githubPagesUrl,
    originalUrl: originalUrl,
    repoName: repoName,
    pagesEnabled: pagesEnabled,
    hasBackend: appInfo.hasBackend,
    note: pagesEnabled
      ? 'GitHub Pages URL will be live in 2-3 minutes'
      : 'GitHub Pages needs to be manually enabled'
  };
}

new QueueEvents('deploy', { connection: redis });
new QueueEvents('deploy-url', { connection: redis });
console.log('Worker ready…');