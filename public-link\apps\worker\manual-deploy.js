import { Octokit } from '@octokit/rest';

const GITHUB_TOKEN = '****************************************';
const octokit = new Octokit({ auth: GITHUB_TOKEN });

async function deployUrl(url) {
  try {
    console.log(`Fetching ${url}...`);
    const response = await fetch(url);
    
    if (!response.ok) {
      throw new Error(`Failed to fetch ${url}: ${response.statusText}`);
    }
    
    const html = await response.text();
    
    // Create repository name
    const repoName = `public-link-${Date.now()}`;
    
    console.log(`Creating GitHub repository: ${repoName}`);
    const repo = await octokit.repos.createForAuthenticatedUser({
      name: repoName,
      description: `Public deployment of ${url}`,
      public: true,
      auto_init: false
    });
    
    console.log(`Repository created: ${repo.data.html_url}`);
    
    // Create and upload index.html
    await octokit.repos.createOrUpdateFileContents({
      owner: repo.data.owner.login,
      repo: repo.data.name,
      path: 'index.html',
      message: 'Initial deployment from Public-Link',
      content: Buffer.from(html).toString('base64')
    });
    
    console.log('Files uploaded to repository');
    
    // Enable GitHub Pages
    try {
      console.log(`Enabling GitHub Pages for ${repoName}...`);
      await octokit.repos.createPagesSite({
        owner: repo.data.owner.login,
        repo: repo.data.name,
        source: {
          branch: 'main',
          path: '/'
        }
      });
      console.log(`GitHub Pages enabled successfully`);
    } catch (pagesError) {
      console.log(`GitHub Pages setup failed:`, pagesError.message);
    }
    
    const githubPagesUrl = `https://${repo.data.owner.login}.github.io/${repo.data.name}`;
    
    console.log('\n🎉 Deployment successful!');
    console.log(`📁 Repository: ${repo.data.html_url}`);
    console.log(`🌐 Public URL: ${githubPagesUrl}`);
    console.log(`⏰ GitHub Pages will be live in 2-3 minutes`);
    
    return {
      githubRepo: repo.data.html_url,
      githubPages: githubPagesUrl,
      repoName: repoName
    };
    
  } catch (error) {
    console.error('Deployment failed:', error.message);
    throw error;
  }
}

// Deploy localhost:5174 (Public-Link Dashboard)
deployUrl('http://localhost:5174')
  .then(result => {
    console.log('\n✅ Deployment completed successfully!');
    console.log('🔗 Your public link:', result.githubPages);
  })
  .catch(error => {
    console.error('❌ Deployment failed:', error.message);
  });
