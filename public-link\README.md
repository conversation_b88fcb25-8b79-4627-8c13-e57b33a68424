# Public-Link Application

Convert any localhost URL into a publicly accessible GitHub Pages link instantly!

## 🚀 Features

- **Instant Deployment**: Convert localhost URLs to public GitHub Pages in seconds
- **Full Asset Support**: Automatically deploys HTML, CSS, and JavaScript files
- **Real-time Status**: Track deployment progress with live updates
- **Fallback Architecture**: Works with or without Redis queue system
- **Clean Interface**: Simple, intuitive dashboard for easy use

## 📋 Prerequisites

- Node.js (v16 or higher)
- pnpm package manager
- GitHub account and Personal Access Token

## 🛠️ Setup Instructions

### 1. Clone the Repository
```bash
git clone https://github.com/appu123455/public-link-main.git
cd public-link-main
```

### 2. Install Dependencies
```bash
pnpm install
```

### 3. Environment Configuration
Copy the example environment file and configure it:
```bash
cp .env.example .env
```

Edit `.env` and add your GitHub Personal Access Token:
```
REDIS_URL=redis://localhost:6379
GITHUB_TOKEN=your-github-token-here
```

### 4. Generate GitHub Personal Access Token
1. Go to GitHub Settings → Developer settings → Personal access tokens
2. Generate a new token with these permissions:
   - `repo` (Full control of private repositories)
   - `public_repo` (Access public repositories)
3. Copy the token and paste it in your `.env` file

### 5. Start the Services

#### Start the API Server
```bash
cd apps/api
pnpm dev
```
The API will run on `http://localhost:4000`

#### Start the Dashboard
```bash
cd apps/dashboard
pnpm dev
```
The dashboard will run on `http://localhost:5174`

#### Optional: Start Redis (for queue functionality)
```bash
# Install Redis and start the server
redis-server
```

## 🎯 How to Use

1. **Open the Dashboard**: Visit `http://localhost:5174`
2. **Enter localhost URL**: Input any localhost URL (e.g., `http://localhost:3000`)
3. **Generate Public Link**: Click "Generate Public Link"
4. **Get Results**: Receive a public GitHub Pages URL in seconds!

## 📁 Project Structure

```
public-link/
├── apps/
│   ├── api/          # Express.js API server
│   ├── dashboard/    # React frontend
│   └── worker/       # BullMQ worker (optional)
├── .env              # Environment variables
└── README.md         # This file
```

## 🔧 API Endpoints

- `POST /deploy-url` - Deploy a localhost URL
- `GET /status/:jobId` - Check deployment status

## 🌐 Deployment Process

1. **Fetch Content**: Downloads HTML, CSS, and JS from localhost URL
2. **Create Repository**: Creates a new GitHub repository
3. **Upload Files**: Uploads all assets to the repository
4. **Enable Pages**: Automatically enables GitHub Pages
5. **Return URL**: Provides the public GitHub Pages URL

## 🛡️ Security Features

- Environment variable configuration for tokens
- No hardcoded secrets in source code
- GitHub push protection compliance

## 🔄 Fallback System

The application works in two modes:
- **With Redis**: Uses BullMQ for job queuing
- **Without Redis**: Direct deployment with in-memory tracking

## 📝 Example Usage

Input: `http://localhost:3000`
Output: `https://username.github.io/public-link-timestamp`

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Submit a pull request

## 📄 License

This project is open source and available under the MIT License.

## 🆘 Troubleshooting

### Common Issues:

1. **"Generating..." stuck**: Ensure API server is running on port 4000
2. **Deployment fails**: Check GitHub token permissions
3. **Assets not loading**: Verify localhost URL is accessible

### Support:
- Check the console logs for detailed error messages
- Ensure all services are running
- Verify environment variables are set correctly
