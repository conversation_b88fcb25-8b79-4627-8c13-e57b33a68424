"use strict";var bn=Object.defineProperty;var a=(t,e)=>bn(t,"name",{value:e,configurable:!0});var ct=require("node:os"),vn=require("tty"),Sn=require("esbuild"),Bn=require("./package-BTMRuUqB.cjs"),he=require("./get-pipe-path-BoR10qr8.cjs"),_u=require("node:url"),$n=require("child_process"),z=require("path"),oe=require("fs"),ke=require("./node-features-roYmp9jK.cjs"),Tn=require("node:path"),xn=require("events"),_e=require("util"),On=require("stream"),Au=require("os"),ue=require("./index-BWFBUo6r.cjs"),Nn=require("node:net"),ft=require("node:fs"),Hn=require("./temporary-directory-B83uKxJF.cjs");require("module");const Pn="known-flag",Ln="unknown-flag",In="argument",{stringify:Ae}=JSON,kn=/\B([A-Z])/g,Mn=a(t=>t.replace(kn,"-$1").toLowerCase(),"v$1"),{hasOwnProperty:Gn}=Object.prototype,ye=a((t,e)=>Gn.call(t,e),"w$2"),Wn=a(t=>Array.isArray(t),"L$2"),yu=a(t=>typeof t=="function"?[t,!1]:Wn(t)?[t[0],!0]:yu(t.type),"b$2"),jn=a((t,e)=>t===Boolean?e!=="false":e,"d$2"),Un=a((t,e)=>typeof e=="boolean"?e:t===Number&&e===""?Number.NaN:t(e),"m$1"),Kn=/[\s.:=]/,Vn=a(t=>{const e=`Flag name ${Ae(t)}`;if(t.length===0)throw new Error(`${e} cannot be empty`);if(t.length===1)throw new Error(`${e} must be longer than a character`);const u=t.match(Kn);if(u)throw new Error(`${e} cannot contain ${Ae(u?.[0])}`)},"B"),zn=a(t=>{const e={},u=a((r,n)=>{if(ye(e,r))throw new Error(`Duplicate flags named ${Ae(r)}`);e[r]=n},"r");for(const r in t){if(!ye(t,r))continue;Vn(r);const n=t[r],s=[[],...yu(n),n];u(r,s);const i=Mn(r);if(r!==i&&u(i,s),"alias"in n&&typeof n.alias=="string"){const{alias:D}=n,o=`Flag alias ${Ae(D)} for flag ${Ae(r)}`;if(D.length===0)throw new Error(`${o} cannot be empty`);if(D.length>1)throw new Error(`${o} must be a single character`);u(D,s)}}return e},"K$1"),Yn=a((t,e)=>{const u={};for(const r in t){if(!ye(t,r))continue;const[n,,s,i]=e[r];if(n.length===0&&"default"in i){let{default:D}=i;typeof D=="function"&&(D=D()),u[r]=D}else u[r]=s?n:n.pop()}return u},"_$2"),Me="--",qn=/[.:=]/,Xn=/^-{1,2}\w/,Qn=a(t=>{if(!Xn.test(t))return;const e=!t.startsWith(Me);let u=t.slice(e?1:2),r;const n=u.match(qn);if(n){const{index:s}=n;r=u.slice(s+1),u=u.slice(0,s)}return[u,r,e]},"N"),Zn=a((t,{onFlag:e,onArgument:u})=>{let r;const n=a((s,i)=>{if(typeof r!="function")return!0;r(s,i),r=void 0},"o");for(let s=0;s<t.length;s+=1){const i=t[s];if(i===Me){n();const o=t.slice(s+1);u?.(o,[s],!0);break}const D=Qn(i);if(D){if(n(),!e)continue;const[o,c,f]=D;if(f)for(let h=0;h<o.length;h+=1){n();const l=h===o.length-1;r=e(o[h],l?c:void 0,[s,h+1,l])}else r=e(o,c,[s])}else n(i,[s])&&u?.([i],[s])}n()},"$$1"),Jn=a((t,e)=>{for(const[u,r,n]of e.reverse()){if(r){const s=t[u];let i=s.slice(0,r);if(n||(i+=s.slice(r+1)),i!=="-"){t[u]=i;continue}}t.splice(u,1)}},"E"),wu=a((t,e=process.argv.slice(2),{ignore:u}={})=>{const r=[],n=zn(t),s={},i=[];return i[Me]=[],Zn(e,{onFlag(D,o,c){const f=ye(n,D);if(!u?.(f?Pn:Ln,D,o)){if(f){const[h,l]=n[D],p=jn(l,o),C=a((g,y)=>{r.push(c),y&&r.push(y),h.push(Un(l,g||""))},"p");return p===void 0?C:C(p)}ye(s,D)||(s[D]=[]),s[D].push(o===void 0?!0:o),r.push(c)}},onArgument(D,o,c){u?.(In,e[o[0]])||(i.push(...D),c?(i[Me]=D,e.splice(o[0])):r.push(o))}}),Jn(e,r),{flags:Yn(t,n),unknownFlags:s,_:i}},"U$2");var es=Object.create,Ge=Object.defineProperty,ts=Object.defineProperties,us=Object.getOwnPropertyDescriptor,rs=Object.getOwnPropertyDescriptors,ns=Object.getOwnPropertyNames,Ru=Object.getOwnPropertySymbols,ss=Object.getPrototypeOf,bu=Object.prototype.hasOwnProperty,is=Object.prototype.propertyIsEnumerable,vu=a((t,e,u)=>e in t?Ge(t,e,{enumerable:!0,configurable:!0,writable:!0,value:u}):t[e]=u,"W$1"),We=a((t,e)=>{for(var u in e||(e={}))bu.call(e,u)&&vu(t,u,e[u]);if(Ru)for(var u of Ru(e))is.call(e,u)&&vu(t,u,e[u]);return t},"p"),ht=a((t,e)=>ts(t,rs(e)),"c"),Ds=a(t=>Ge(t,"__esModule",{value:!0}),"nD"),os=a((t,e)=>()=>(t&&(e=t(t=0)),e),"rD"),as=a((t,e)=>()=>(e||t((e={exports:{}}).exports,e),e.exports),"iD"),ls=a((t,e,u,r)=>{if(e&&typeof e=="object"||typeof e=="function")for(let n of ns(e))!bu.call(t,n)&&n!=="default"&&Ge(t,n,{get:a(()=>e[n],"get"),enumerable:!(r=us(e,n))||r.enumerable});return t},"oD"),cs=a((t,e)=>ls(Ds(Ge(t!=null?es(ss(t)):{},"default",{value:t,enumerable:!0})),t),"BD"),K=os(()=>{}),fs=as((t,e)=>{K(),e.exports=function(){return/\uD83C\uDFF4\uDB40\uDC67\uDB40\uDC62(?:\uDB40\uDC77\uDB40\uDC6C\uDB40\uDC73|\uDB40\uDC73\uDB40\uDC63\uDB40\uDC74|\uDB40\uDC65\uDB40\uDC6E\uDB40\uDC67)\uDB40\uDC7F|(?:\uD83E\uDDD1\uD83C\uDFFF\u200D\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFF\u200D\uD83E\uDD1D\u200D(?:\uD83D[\uDC68\uDC69]))(?:\uD83C[\uDFFB-\uDFFE])|(?:\uD83E\uDDD1\uD83C\uDFFE\u200D\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFE\u200D\uD83E\uDD1D\u200D(?:\uD83D[\uDC68\uDC69]))(?:\uD83C[\uDFFB-\uDFFD\uDFFF])|(?:\uD83E\uDDD1\uD83C\uDFFD\u200D\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFD\u200D\uD83E\uDD1D\u200D(?:\uD83D[\uDC68\uDC69]))(?:\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])|(?:\uD83E\uDDD1\uD83C\uDFFC\u200D\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFC\u200D\uD83E\uDD1D\u200D(?:\uD83D[\uDC68\uDC69]))(?:\uD83C[\uDFFB\uDFFD-\uDFFF])|(?:\uD83E\uDDD1\uD83C\uDFFB\u200D\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFB\u200D\uD83E\uDD1D\u200D(?:\uD83D[\uDC68\uDC69]))(?:\uD83C[\uDFFC-\uDFFF])|\uD83D\uDC68(?:\uD83C\uDFFB(?:\u200D(?:\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFF])|\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFF]))|\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFC-\uDFFF])|[\u2695\u2696\u2708]\uFE0F|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD]))?|(?:\uD83C[\uDFFC-\uDFFF])\u200D\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFF])|\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFF]))|\u200D(?:\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D)?\uD83D\uDC68|(?:\uD83D[\uDC68\uDC69])\u200D(?:\uD83D\uDC66\u200D\uD83D\uDC66|\uD83D\uDC67\u200D(?:\uD83D[\uDC66\uDC67]))|\uD83D\uDC66\u200D\uD83D\uDC66|\uD83D\uDC67\u200D(?:\uD83D[\uDC66\uDC67])|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFF\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFE])|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFE\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFD\uDFFF])|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFD\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFC\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB\uDFFD-\uDFFF])|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|(?:\uD83C\uDFFF\u200D[\u2695\u2696\u2708]|\uD83C\uDFFE\u200D[\u2695\u2696\u2708]|\uD83C\uDFFD\u200D[\u2695\u2696\u2708]|\uD83C\uDFFC\u200D[\u2695\u2696\u2708]|\u200D[\u2695\u2696\u2708])\uFE0F|\u200D(?:(?:\uD83D[\uDC68\uDC69])\u200D(?:\uD83D[\uDC66\uDC67])|\uD83D[\uDC66\uDC67])|\uD83C\uDFFF|\uD83C\uDFFE|\uD83C\uDFFD|\uD83C\uDFFC)?|(?:\uD83D\uDC69(?:\uD83C\uDFFB\u200D\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D(?:\uD83D[\uDC68\uDC69])|\uD83D[\uDC68\uDC69])|(?:\uD83C[\uDFFC-\uDFFF])\u200D\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D(?:\uD83D[\uDC68\uDC69])|\uD83D[\uDC68\uDC69]))|\uD83E\uDDD1(?:\uD83C[\uDFFB-\uDFFF])\u200D\uD83E\uDD1D\u200D\uD83E\uDDD1)(?:\uD83C[\uDFFB-\uDFFF])|\uD83D\uDC69\u200D\uD83D\uDC69\u200D(?:\uD83D\uDC66\u200D\uD83D\uDC66|\uD83D\uDC67\u200D(?:\uD83D[\uDC66\uDC67]))|\uD83D\uDC69(?:\u200D(?:\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D(?:\uD83D[\uDC68\uDC69])|\uD83D[\uDC68\uDC69])|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFF\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFE\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFD\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFC\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFB\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD]))|\uD83E\uDDD1(?:\u200D(?:\uD83E\uDD1D\u200D\uD83E\uDDD1|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFF\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFE\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFD\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFC\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFB\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD]))|\uD83D\uDC69\u200D\uD83D\uDC66\u200D\uD83D\uDC66|\uD83D\uDC69\u200D\uD83D\uDC69\u200D(?:\uD83D[\uDC66\uDC67])|\uD83D\uDC69\u200D\uD83D\uDC67\u200D(?:\uD83D[\uDC66\uDC67])|(?:\uD83D\uDC41\uFE0F\u200D\uD83D\uDDE8|\uD83E\uDDD1(?:\uD83C\uDFFF\u200D[\u2695\u2696\u2708]|\uD83C\uDFFE\u200D[\u2695\u2696\u2708]|\uD83C\uDFFD\u200D[\u2695\u2696\u2708]|\uD83C\uDFFC\u200D[\u2695\u2696\u2708]|\uD83C\uDFFB\u200D[\u2695\u2696\u2708]|\u200D[\u2695\u2696\u2708])|\uD83D\uDC69(?:\uD83C\uDFFF\u200D[\u2695\u2696\u2708]|\uD83C\uDFFE\u200D[\u2695\u2696\u2708]|\uD83C\uDFFD\u200D[\u2695\u2696\u2708]|\uD83C\uDFFC\u200D[\u2695\u2696\u2708]|\uD83C\uDFFB\u200D[\u2695\u2696\u2708]|\u200D[\u2695\u2696\u2708])|\uD83D\uDE36\u200D\uD83C\uDF2B|\uD83C\uDFF3\uFE0F\u200D\u26A7|\uD83D\uDC3B\u200D\u2744|(?:(?:\uD83C[\uDFC3\uDFC4\uDFCA]|\uD83D[\uDC6E\uDC70\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4-\uDEB6]|\uD83E[\uDD26\uDD35\uDD37-\uDD39\uDD3D\uDD3E\uDDB8\uDDB9\uDDCD-\uDDCF\uDDD4\uDDD6-\uDDDD])(?:\uD83C[\uDFFB-\uDFFF])|\uD83D\uDC6F|\uD83E[\uDD3C\uDDDE\uDDDF])\u200D[\u2640\u2642]|(?:\u26F9|\uD83C[\uDFCB\uDFCC]|\uD83D\uDD75)(?:\uFE0F|\uD83C[\uDFFB-\uDFFF])\u200D[\u2640\u2642]|\uD83C\uDFF4\u200D\u2620|(?:\uD83C[\uDFC3\uDFC4\uDFCA]|\uD83D[\uDC6E\uDC70\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4-\uDEB6]|\uD83E[\uDD26\uDD35\uDD37-\uDD39\uDD3D\uDD3E\uDDB8\uDDB9\uDDCD-\uDDCF\uDDD4\uDDD6-\uDDDD])\u200D[\u2640\u2642]|[\xA9\xAE\u203C\u2049\u2122\u2139\u2194-\u2199\u21A9\u21AA\u2328\u23CF\u23ED-\u23EF\u23F1\u23F2\u23F8-\u23FA\u24C2\u25AA\u25AB\u25B6\u25C0\u25FB\u25FC\u2600-\u2604\u260E\u2611\u2618\u2620\u2622\u2623\u2626\u262A\u262E\u262F\u2638-\u263A\u2640\u2642\u265F\u2660\u2663\u2665\u2666\u2668\u267B\u267E\u2692\u2694-\u2697\u2699\u269B\u269C\u26A0\u26A7\u26B0\u26B1\u26C8\u26CF\u26D1\u26D3\u26E9\u26F0\u26F1\u26F4\u26F7\u26F8\u2702\u2708\u2709\u270F\u2712\u2714\u2716\u271D\u2721\u2733\u2734\u2744\u2747\u2763\u27A1\u2934\u2935\u2B05-\u2B07\u3030\u303D\u3297\u3299]|\uD83C[\uDD70\uDD71\uDD7E\uDD7F\uDE02\uDE37\uDF21\uDF24-\uDF2C\uDF36\uDF7D\uDF96\uDF97\uDF99-\uDF9B\uDF9E\uDF9F\uDFCD\uDFCE\uDFD4-\uDFDF\uDFF5\uDFF7]|\uD83D[\uDC3F\uDCFD\uDD49\uDD4A\uDD6F\uDD70\uDD73\uDD76-\uDD79\uDD87\uDD8A-\uDD8D\uDDA5\uDDA8\uDDB1\uDDB2\uDDBC\uDDC2-\uDDC4\uDDD1-\uDDD3\uDDDC-\uDDDE\uDDE1\uDDE3\uDDE8\uDDEF\uDDF3\uDDFA\uDECB\uDECD-\uDECF\uDEE0-\uDEE5\uDEE9\uDEF0\uDEF3])\uFE0F|\uD83C\uDFF3\uFE0F\u200D\uD83C\uDF08|\uD83D\uDC69\u200D\uD83D\uDC67|\uD83D\uDC69\u200D\uD83D\uDC66|\uD83D\uDE35\u200D\uD83D\uDCAB|\uD83D\uDE2E\u200D\uD83D\uDCA8|\uD83D\uDC15\u200D\uD83E\uDDBA|\uD83E\uDDD1(?:\uD83C\uDFFF|\uD83C\uDFFE|\uD83C\uDFFD|\uD83C\uDFFC|\uD83C\uDFFB)?|\uD83D\uDC69(?:\uD83C\uDFFF|\uD83C\uDFFE|\uD83C\uDFFD|\uD83C\uDFFC|\uD83C\uDFFB)?|\uD83C\uDDFD\uD83C\uDDF0|\uD83C\uDDF6\uD83C\uDDE6|\uD83C\uDDF4\uD83C\uDDF2|\uD83D\uDC08\u200D\u2B1B|\u2764\uFE0F\u200D(?:\uD83D\uDD25|\uD83E\uDE79)|\uD83D\uDC41\uFE0F|\uD83C\uDFF3\uFE0F|\uD83C\uDDFF(?:\uD83C[\uDDE6\uDDF2\uDDFC])|\uD83C\uDDFE(?:\uD83C[\uDDEA\uDDF9])|\uD83C\uDDFC(?:\uD83C[\uDDEB\uDDF8])|\uD83C\uDDFB(?:\uD83C[\uDDE6\uDDE8\uDDEA\uDDEC\uDDEE\uDDF3\uDDFA])|\uD83C\uDDFA(?:\uD83C[\uDDE6\uDDEC\uDDF2\uDDF3\uDDF8\uDDFE\uDDFF])|\uD83C\uDDF9(?:\uD83C[\uDDE6\uDDE8\uDDE9\uDDEB-\uDDED\uDDEF-\uDDF4\uDDF7\uDDF9\uDDFB\uDDFC\uDDFF])|\uD83C\uDDF8(?:\uD83C[\uDDE6-\uDDEA\uDDEC-\uDDF4\uDDF7-\uDDF9\uDDFB\uDDFD-\uDDFF])|\uD83C\uDDF7(?:\uD83C[\uDDEA\uDDF4\uDDF8\uDDFA\uDDFC])|\uD83C\uDDF5(?:\uD83C[\uDDE6\uDDEA-\uDDED\uDDF0-\uDDF3\uDDF7-\uDDF9\uDDFC\uDDFE])|\uD83C\uDDF3(?:\uD83C[\uDDE6\uDDE8\uDDEA-\uDDEC\uDDEE\uDDF1\uDDF4\uDDF5\uDDF7\uDDFA\uDDFF])|\uD83C\uDDF2(?:\uD83C[\uDDE6\uDDE8-\uDDED\uDDF0-\uDDFF])|\uD83C\uDDF1(?:\uD83C[\uDDE6-\uDDE8\uDDEE\uDDF0\uDDF7-\uDDFB\uDDFE])|\uD83C\uDDF0(?:\uD83C[\uDDEA\uDDEC-\uDDEE\uDDF2\uDDF3\uDDF5\uDDF7\uDDFC\uDDFE\uDDFF])|\uD83C\uDDEF(?:\uD83C[\uDDEA\uDDF2\uDDF4\uDDF5])|\uD83C\uDDEE(?:\uD83C[\uDDE8-\uDDEA\uDDF1-\uDDF4\uDDF6-\uDDF9])|\uD83C\uDDED(?:\uD83C[\uDDF0\uDDF2\uDDF3\uDDF7\uDDF9\uDDFA])|\uD83C\uDDEC(?:\uD83C[\uDDE6\uDDE7\uDDE9-\uDDEE\uDDF1-\uDDF3\uDDF5-\uDDFA\uDDFC\uDDFE])|\uD83C\uDDEB(?:\uD83C[\uDDEE-\uDDF0\uDDF2\uDDF4\uDDF7])|\uD83C\uDDEA(?:\uD83C[\uDDE6\uDDE8\uDDEA\uDDEC\uDDED\uDDF7-\uDDFA])|\uD83C\uDDE9(?:\uD83C[\uDDEA\uDDEC\uDDEF\uDDF0\uDDF2\uDDF4\uDDFF])|\uD83C\uDDE8(?:\uD83C[\uDDE6\uDDE8\uDDE9\uDDEB-\uDDEE\uDDF0-\uDDF5\uDDF7\uDDFA-\uDDFF])|\uD83C\uDDE7(?:\uD83C[\uDDE6\uDDE7\uDDE9-\uDDEF\uDDF1-\uDDF4\uDDF6-\uDDF9\uDDFB\uDDFC\uDDFE\uDDFF])|\uD83C\uDDE6(?:\uD83C[\uDDE8-\uDDEC\uDDEE\uDDF1\uDDF2\uDDF4\uDDF6-\uDDFA\uDDFC\uDDFD\uDDFF])|[#\*0-9]\uFE0F\u20E3|\u2764\uFE0F|(?:\uD83C[\uDFC3\uDFC4\uDFCA]|\uD83D[\uDC6E\uDC70\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4-\uDEB6]|\uD83E[\uDD26\uDD35\uDD37-\uDD39\uDD3D\uDD3E\uDDB8\uDDB9\uDDCD-\uDDCF\uDDD4\uDDD6-\uDDDD])(?:\uD83C[\uDFFB-\uDFFF])|(?:\u26F9|\uD83C[\uDFCB\uDFCC]|\uD83D\uDD75)(?:\uFE0F|\uD83C[\uDFFB-\uDFFF])|\uD83C\uDFF4|(?:[\u270A\u270B]|\uD83C[\uDF85\uDFC2\uDFC7]|\uD83D[\uDC42\uDC43\uDC46-\uDC50\uDC66\uDC67\uDC6B-\uDC6D\uDC72\uDC74-\uDC76\uDC78\uDC7C\uDC83\uDC85\uDC8F\uDC91\uDCAA\uDD7A\uDD95\uDD96\uDE4C\uDE4F\uDEC0\uDECC]|\uD83E[\uDD0C\uDD0F\uDD18-\uDD1C\uDD1E\uDD1F\uDD30-\uDD34\uDD36\uDD77\uDDB5\uDDB6\uDDBB\uDDD2\uDDD3\uDDD5])(?:\uD83C[\uDFFB-\uDFFF])|(?:[\u261D\u270C\u270D]|\uD83D[\uDD74\uDD90])(?:\uFE0F|\uD83C[\uDFFB-\uDFFF])|[\u270A\u270B]|\uD83C[\uDF85\uDFC2\uDFC7]|\uD83D[\uDC08\uDC15\uDC3B\uDC42\uDC43\uDC46-\uDC50\uDC66\uDC67\uDC6B-\uDC6D\uDC72\uDC74-\uDC76\uDC78\uDC7C\uDC83\uDC85\uDC8F\uDC91\uDCAA\uDD7A\uDD95\uDD96\uDE2E\uDE35\uDE36\uDE4C\uDE4F\uDEC0\uDECC]|\uD83E[\uDD0C\uDD0F\uDD18-\uDD1C\uDD1E\uDD1F\uDD30-\uDD34\uDD36\uDD77\uDDB5\uDDB6\uDDBB\uDDD2\uDDD3\uDDD5]|\uD83C[\uDFC3\uDFC4\uDFCA]|\uD83D[\uDC6E\uDC70\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4-\uDEB6]|\uD83E[\uDD26\uDD35\uDD37-\uDD39\uDD3D\uDD3E\uDDB8\uDDB9\uDDCD-\uDDCF\uDDD4\uDDD6-\uDDDD]|\uD83D\uDC6F|\uD83E[\uDD3C\uDDDE\uDDDF]|[\u231A\u231B\u23E9-\u23EC\u23F0\u23F3\u25FD\u25FE\u2614\u2615\u2648-\u2653\u267F\u2693\u26A1\u26AA\u26AB\u26BD\u26BE\u26C4\u26C5\u26CE\u26D4\u26EA\u26F2\u26F3\u26F5\u26FA\u26FD\u2705\u2728\u274C\u274E\u2753-\u2755\u2757\u2795-\u2797\u27B0\u27BF\u2B1B\u2B1C\u2B50\u2B55]|\uD83C[\uDC04\uDCCF\uDD8E\uDD91-\uDD9A\uDE01\uDE1A\uDE2F\uDE32-\uDE36\uDE38-\uDE3A\uDE50\uDE51\uDF00-\uDF20\uDF2D-\uDF35\uDF37-\uDF7C\uDF7E-\uDF84\uDF86-\uDF93\uDFA0-\uDFC1\uDFC5\uDFC6\uDFC8\uDFC9\uDFCF-\uDFD3\uDFE0-\uDFF0\uDFF8-\uDFFF]|\uD83D[\uDC00-\uDC07\uDC09-\uDC14\uDC16-\uDC3A\uDC3C-\uDC3E\uDC40\uDC44\uDC45\uDC51-\uDC65\uDC6A\uDC79-\uDC7B\uDC7D-\uDC80\uDC84\uDC88-\uDC8E\uDC90\uDC92-\uDCA9\uDCAB-\uDCFC\uDCFF-\uDD3D\uDD4B-\uDD4E\uDD50-\uDD67\uDDA4\uDDFB-\uDE2D\uDE2F-\uDE34\uDE37-\uDE44\uDE48-\uDE4A\uDE80-\uDEA2\uDEA4-\uDEB3\uDEB7-\uDEBF\uDEC1-\uDEC5\uDED0-\uDED2\uDED5-\uDED7\uDEEB\uDEEC\uDEF4-\uDEFC\uDFE0-\uDFEB]|\uD83E[\uDD0D\uDD0E\uDD10-\uDD17\uDD1D\uDD20-\uDD25\uDD27-\uDD2F\uDD3A\uDD3F-\uDD45\uDD47-\uDD76\uDD78\uDD7A-\uDDB4\uDDB7\uDDBA\uDDBC-\uDDCB\uDDD0\uDDE0-\uDDFF\uDE70-\uDE74\uDE78-\uDE7A\uDE80-\uDE86\uDE90-\uDEA8\uDEB0-\uDEB6\uDEC0-\uDEC2\uDED0-\uDED6]|(?:[\u231A\u231B\u23E9-\u23EC\u23F0\u23F3\u25FD\u25FE\u2614\u2615\u2648-\u2653\u267F\u2693\u26A1\u26AA\u26AB\u26BD\u26BE\u26C4\u26C5\u26CE\u26D4\u26EA\u26F2\u26F3\u26F5\u26FA\u26FD\u2705\u270A\u270B\u2728\u274C\u274E\u2753-\u2755\u2757\u2795-\u2797\u27B0\u27BF\u2B1B\u2B1C\u2B50\u2B55]|\uD83C[\uDC04\uDCCF\uDD8E\uDD91-\uDD9A\uDDE6-\uDDFF\uDE01\uDE1A\uDE2F\uDE32-\uDE36\uDE38-\uDE3A\uDE50\uDE51\uDF00-\uDF20\uDF2D-\uDF35\uDF37-\uDF7C\uDF7E-\uDF93\uDFA0-\uDFCA\uDFCF-\uDFD3\uDFE0-\uDFF0\uDFF4\uDFF8-\uDFFF]|\uD83D[\uDC00-\uDC3E\uDC40\uDC42-\uDCFC\uDCFF-\uDD3D\uDD4B-\uDD4E\uDD50-\uDD67\uDD7A\uDD95\uDD96\uDDA4\uDDFB-\uDE4F\uDE80-\uDEC5\uDECC\uDED0-\uDED2\uDED5-\uDED7\uDEEB\uDEEC\uDEF4-\uDEFC\uDFE0-\uDFEB]|\uD83E[\uDD0C-\uDD3A\uDD3C-\uDD45\uDD47-\uDD78\uDD7A-\uDDCB\uDDCD-\uDDFF\uDE70-\uDE74\uDE78-\uDE7A\uDE80-\uDE86\uDE90-\uDEA8\uDEB0-\uDEB6\uDEC0-\uDEC2\uDED0-\uDED6])|(?:[#\*0-9\xA9\xAE\u203C\u2049\u2122\u2139\u2194-\u2199\u21A9\u21AA\u231A\u231B\u2328\u23CF\u23E9-\u23F3\u23F8-\u23FA\u24C2\u25AA\u25AB\u25B6\u25C0\u25FB-\u25FE\u2600-\u2604\u260E\u2611\u2614\u2615\u2618\u261D\u2620\u2622\u2623\u2626\u262A\u262E\u262F\u2638-\u263A\u2640\u2642\u2648-\u2653\u265F\u2660\u2663\u2665\u2666\u2668\u267B\u267E\u267F\u2692-\u2697\u2699\u269B\u269C\u26A0\u26A1\u26A7\u26AA\u26AB\u26B0\u26B1\u26BD\u26BE\u26C4\u26C5\u26C8\u26CE\u26CF\u26D1\u26D3\u26D4\u26E9\u26EA\u26F0-\u26F5\u26F7-\u26FA\u26FD\u2702\u2705\u2708-\u270D\u270F\u2712\u2714\u2716\u271D\u2721\u2728\u2733\u2734\u2744\u2747\u274C\u274E\u2753-\u2755\u2757\u2763\u2764\u2795-\u2797\u27A1\u27B0\u27BF\u2934\u2935\u2B05-\u2B07\u2B1B\u2B1C\u2B50\u2B55\u3030\u303D\u3297\u3299]|\uD83C[\uDC04\uDCCF\uDD70\uDD71\uDD7E\uDD7F\uDD8E\uDD91-\uDD9A\uDDE6-\uDDFF\uDE01\uDE02\uDE1A\uDE2F\uDE32-\uDE3A\uDE50\uDE51\uDF00-\uDF21\uDF24-\uDF93\uDF96\uDF97\uDF99-\uDF9B\uDF9E-\uDFF0\uDFF3-\uDFF5\uDFF7-\uDFFF]|\uD83D[\uDC00-\uDCFD\uDCFF-\uDD3D\uDD49-\uDD4E\uDD50-\uDD67\uDD6F\uDD70\uDD73-\uDD7A\uDD87\uDD8A-\uDD8D\uDD90\uDD95\uDD96\uDDA4\uDDA5\uDDA8\uDDB1\uDDB2\uDDBC\uDDC2-\uDDC4\uDDD1-\uDDD3\uDDDC-\uDDDE\uDDE1\uDDE3\uDDE8\uDDEF\uDDF3\uDDFA-\uDE4F\uDE80-\uDEC5\uDECB-\uDED2\uDED5-\uDED7\uDEE0-\uDEE5\uDEE9\uDEEB\uDEEC\uDEF0\uDEF3-\uDEFC\uDFE0-\uDFEB]|\uD83E[\uDD0C-\uDD3A\uDD3C-\uDD45\uDD47-\uDD78\uDD7A-\uDDCB\uDDCD-\uDDFF\uDE70-\uDE74\uDE78-\uDE7A\uDE80-\uDE86\uDE90-\uDEA8\uDEB0-\uDEB6\uDEC0-\uDEC2\uDED0-\uDED6])\uFE0F|(?:[\u261D\u26F9\u270A-\u270D]|\uD83C[\uDF85\uDFC2-\uDFC4\uDFC7\uDFCA-\uDFCC]|\uD83D[\uDC42\uDC43\uDC46-\uDC50\uDC66-\uDC78\uDC7C\uDC81-\uDC83\uDC85-\uDC87\uDC8F\uDC91\uDCAA\uDD74\uDD75\uDD7A\uDD90\uDD95\uDD96\uDE45-\uDE47\uDE4B-\uDE4F\uDEA3\uDEB4-\uDEB6\uDEC0\uDECC]|\uD83E[\uDD0C\uDD0F\uDD18-\uDD1F\uDD26\uDD30-\uDD39\uDD3C-\uDD3E\uDD77\uDDB5\uDDB6\uDDB8\uDDB9\uDDBB\uDDCD-\uDDCF\uDDD1-\uDDDD])/g}});K(),K(),K();var hs=a(t=>{var e,u,r;let n=(e=process.stdout.columns)!=null?e:Number.POSITIVE_INFINITY;return typeof t=="function"&&(t=t(n)),t||(t={}),Array.isArray(t)?{columns:t,stdoutColumns:n}:{columns:(u=t.columns)!=null?u:[],stdoutColumns:(r=t.stdoutColumns)!=null?r:n}},"v");K(),K(),K(),K(),K();function ds({onlyFirst:t=!1}={}){let e=["[\\u001B\\u009B][[\\]()#;?]*(?:(?:(?:(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]+)*|[a-zA-Z\\d]+(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]*)*)?\\u0007)","(?:(?:\\d{1,4}(?:;\\d{0,4})*)?[\\dA-PR-TZcf-ntqry=><~]))"].join("|");return new RegExp(e,t?void 0:"g")}a(ds,"w$1");function Su(t){if(typeof t!="string")throw new TypeError(`Expected a \`string\`, got \`${typeof t}\``);return t.replace(ds(),"")}a(Su,"d$1"),K();function Es(t){return Number.isInteger(t)?t>=4352&&(t<=4447||t===9001||t===9002||11904<=t&&t<=12871&&t!==12351||12880<=t&&t<=19903||19968<=t&&t<=42182||43360<=t&&t<=43388||44032<=t&&t<=55203||63744<=t&&t<=64255||65040<=t&&t<=65049||65072<=t&&t<=65131||65281<=t&&t<=65376||65504<=t&&t<=65510||110592<=t&&t<=110593||127488<=t&&t<=127569||131072<=t&&t<=262141):!1}a(Es,"y$1");var ps=cs(fs());function ae(t){if(typeof t!="string"||t.length===0||(t=Su(t),t.length===0))return 0;t=t.replace((0,ps.default)(),"  ");let e=0;for(let u=0;u<t.length;u++){let r=t.codePointAt(u);r<=31||r>=127&&r<=159||r>=768&&r<=879||(r>65535&&u++,e+=Es(r)?2:1)}return e}a(ae,"g");var Bu=a(t=>Math.max(...t.split(`
`).map(ae)),"b$1"),Cs=a(t=>{let e=[];for(let u of t){let{length:r}=u,n=r-e.length;for(let s=0;s<n;s+=1)e.push(0);for(let s=0;s<r;s+=1){let i=Bu(u[s]);i>e[s]&&(e[s]=i)}}return e},"k$1");K();var $u=/^\d+%$/,Tu={width:"auto",align:"left",contentWidth:0,paddingLeft:0,paddingRight:0,paddingTop:0,paddingBottom:0,horizontalPadding:0,paddingLeftString:"",paddingRightString:""},Fs=a((t,e)=>{var u;let r=[];for(let n=0;n<t.length;n+=1){let s=(u=e[n])!=null?u:"auto";if(typeof s=="number"||s==="auto"||s==="content-width"||typeof s=="string"&&$u.test(s)){r.push(ht(We({},Tu),{width:s,contentWidth:t[n]}));continue}if(s&&typeof s=="object"){let i=ht(We(We({},Tu),s),{contentWidth:t[n]});i.horizontalPadding=i.paddingLeft+i.paddingRight,r.push(i);continue}throw new Error(`Invalid column width: ${JSON.stringify(s)}`)}return r},"sD");function gs(t,e){for(let u of t){let{width:r}=u;if(r==="content-width"&&(u.width=u.contentWidth),r==="auto"){let o=Math.min(20,u.contentWidth);u.width=o,u.autoOverflow=u.contentWidth-o}if(typeof r=="string"&&$u.test(r)){let o=Number.parseFloat(r.slice(0,-1))/100;u.width=Math.floor(e*o)-(u.paddingLeft+u.paddingRight)}let{horizontalPadding:n}=u,s=1,i=s+n;if(i>=e){let o=i-e,c=Math.ceil(u.paddingLeft/n*o),f=o-c;u.paddingLeft-=c,u.paddingRight-=f,u.horizontalPadding=u.paddingLeft+u.paddingRight}u.paddingLeftString=u.paddingLeft?" ".repeat(u.paddingLeft):"",u.paddingRightString=u.paddingRight?" ".repeat(u.paddingRight):"";let D=e-u.horizontalPadding;u.width=Math.max(Math.min(u.width,D),s)}}a(gs,"aD");var xu=a(()=>Object.assign([],{columns:0}),"G$1");function ms(t,e){let u=[xu()],[r]=u;for(let n of t){let s=n.width+n.horizontalPadding;r.columns+s>e&&(r=xu(),u.push(r)),r.push(n),r.columns+=s}for(let n of u){let s=n.reduce((l,p)=>l+p.width+p.horizontalPadding,0),i=e-s;if(i===0)continue;let D=n.filter(l=>"autoOverflow"in l),o=D.filter(l=>l.autoOverflow>0),c=o.reduce((l,p)=>l+p.autoOverflow,0),f=Math.min(c,i);for(let l of o){let p=Math.floor(l.autoOverflow/c*f);l.width+=p,i-=p}let h=Math.floor(i/D.length);for(let l=0;l<D.length;l+=1){let p=D[l];l===D.length-1?p.width+=i:p.width+=h,i-=h}}return u}a(ms,"lD");function _s(t,e,u){let r=Fs(u,e);return gs(r,t),ms(r,t)}a(_s,"Z$1"),K(),K(),K();var dt=10,Ou=a((t=0)=>e=>`\x1B[${e+t}m`,"U$1"),Nu=a((t=0)=>e=>`\x1B[${38+t};5;${e}m`,"V$1"),Hu=a((t=0)=>(e,u,r)=>`\x1B[${38+t};2;${e};${u};${r}m`,"Y");function As(){let t=new Map,e={modifier:{reset:[0,0],bold:[1,22],dim:[2,22],italic:[3,23],underline:[4,24],overline:[53,55],inverse:[7,27],hidden:[8,28],strikethrough:[9,29]},color:{black:[30,39],red:[31,39],green:[32,39],yellow:[33,39],blue:[34,39],magenta:[35,39],cyan:[36,39],white:[37,39],blackBright:[90,39],redBright:[91,39],greenBright:[92,39],yellowBright:[93,39],blueBright:[94,39],magentaBright:[95,39],cyanBright:[96,39],whiteBright:[97,39]},bgColor:{bgBlack:[40,49],bgRed:[41,49],bgGreen:[42,49],bgYellow:[43,49],bgBlue:[44,49],bgMagenta:[45,49],bgCyan:[46,49],bgWhite:[47,49],bgBlackBright:[100,49],bgRedBright:[101,49],bgGreenBright:[102,49],bgYellowBright:[103,49],bgBlueBright:[104,49],bgMagentaBright:[105,49],bgCyanBright:[106,49],bgWhiteBright:[107,49]}};e.color.gray=e.color.blackBright,e.bgColor.bgGray=e.bgColor.bgBlackBright,e.color.grey=e.color.blackBright,e.bgColor.bgGrey=e.bgColor.bgBlackBright;for(let[u,r]of Object.entries(e)){for(let[n,s]of Object.entries(r))e[n]={open:`\x1B[${s[0]}m`,close:`\x1B[${s[1]}m`},r[n]=e[n],t.set(s[0],s[1]);Object.defineProperty(e,u,{value:r,enumerable:!1})}return Object.defineProperty(e,"codes",{value:t,enumerable:!1}),e.color.close="\x1B[39m",e.bgColor.close="\x1B[49m",e.color.ansi=Ou(),e.color.ansi256=Nu(),e.color.ansi16m=Hu(),e.bgColor.ansi=Ou(dt),e.bgColor.ansi256=Nu(dt),e.bgColor.ansi16m=Hu(dt),Object.defineProperties(e,{rgbToAnsi256:{value:a((u,r,n)=>u===r&&r===n?u<8?16:u>248?231:Math.round((u-8)/247*24)+232:16+36*Math.round(u/255*5)+6*Math.round(r/255*5)+Math.round(n/255*5),"value"),enumerable:!1},hexToRgb:{value:a(u=>{let r=/(?<colorString>[a-f\d]{6}|[a-f\d]{3})/i.exec(u.toString(16));if(!r)return[0,0,0];let{colorString:n}=r.groups;n.length===3&&(n=n.split("").map(i=>i+i).join(""));let s=Number.parseInt(n,16);return[s>>16&255,s>>8&255,s&255]},"value"),enumerable:!1},hexToAnsi256:{value:a(u=>e.rgbToAnsi256(...e.hexToRgb(u)),"value"),enumerable:!1},ansi256ToAnsi:{value:a(u=>{if(u<8)return 30+u;if(u<16)return 90+(u-8);let r,n,s;if(u>=232)r=((u-232)*10+8)/255,n=r,s=r;else{u-=16;let o=u%36;r=Math.floor(u/36)/5,n=Math.floor(o/6)/5,s=o%6/5}let i=Math.max(r,n,s)*2;if(i===0)return 30;let D=30+(Math.round(s)<<2|Math.round(n)<<1|Math.round(r));return i===2&&(D+=60),D},"value"),enumerable:!1},rgbToAnsi:{value:a((u,r,n)=>e.ansi256ToAnsi(e.rgbToAnsi256(u,r,n)),"value"),enumerable:!1},hexToAnsi:{value:a(u=>e.ansi256ToAnsi(e.hexToAnsi256(u)),"value"),enumerable:!1}}),e}a(As,"AD");var ys=As(),ws=ys,je=new Set(["\x1B","\x9B"]),Rs=39,Et="\x07",Pu="[",bs="]",Lu="m",pt=`${bs}8;;`,Iu=a(t=>`${je.values().next().value}${Pu}${t}${Lu}`,"J$1"),ku=a(t=>`${je.values().next().value}${pt}${t}${Et}`,"Q"),vs=a(t=>t.split(" ").map(e=>ae(e)),"hD"),Ct=a((t,e,u)=>{let r=[...e],n=!1,s=!1,i=ae(Su(t[t.length-1]));for(let[D,o]of r.entries()){let c=ae(o);if(i+c<=u?t[t.length-1]+=o:(t.push(o),i=0),je.has(o)&&(n=!0,s=r.slice(D+1).join("").startsWith(pt)),n){s?o===Et&&(n=!1,s=!1):o===Lu&&(n=!1);continue}i+=c,i===u&&D<r.length-1&&(t.push(""),i=0)}!i&&t[t.length-1].length>0&&t.length>1&&(t[t.length-2]+=t.pop())},"S$1"),Ss=a(t=>{let e=t.split(" "),u=e.length;for(;u>0&&!(ae(e[u-1])>0);)u--;return u===e.length?t:e.slice(0,u).join(" ")+e.slice(u).join("")},"cD"),Bs=a((t,e,u={})=>{if(u.trim!==!1&&t.trim()==="")return"";let r="",n,s,i=vs(t),D=[""];for(let[c,f]of t.split(" ").entries()){u.trim!==!1&&(D[D.length-1]=D[D.length-1].trimStart());let h=ae(D[D.length-1]);if(c!==0&&(h>=e&&(u.wordWrap===!1||u.trim===!1)&&(D.push(""),h=0),(h>0||u.trim===!1)&&(D[D.length-1]+=" ",h++)),u.hard&&i[c]>e){let l=e-h,p=1+Math.floor((i[c]-l-1)/e);Math.floor((i[c]-1)/e)<p&&D.push(""),Ct(D,f,e);continue}if(h+i[c]>e&&h>0&&i[c]>0){if(u.wordWrap===!1&&h<e){Ct(D,f,e);continue}D.push("")}if(h+i[c]>e&&u.wordWrap===!1){Ct(D,f,e);continue}D[D.length-1]+=f}u.trim!==!1&&(D=D.map(c=>Ss(c)));let o=[...D.join(`
`)];for(let[c,f]of o.entries()){if(r+=f,je.has(f)){let{groups:l}=new RegExp(`(?:\\${Pu}(?<code>\\d+)m|\\${pt}(?<uri>.*)${Et})`).exec(o.slice(c).join(""))||{groups:{}};if(l.code!==void 0){let p=Number.parseFloat(l.code);n=p===Rs?void 0:p}else l.uri!==void 0&&(s=l.uri.length===0?void 0:l.uri)}let h=ws.codes.get(Number(n));o[c+1]===`
`?(s&&(r+=ku("")),n&&h&&(r+=Iu(h))):f===`
`&&(n&&h&&(r+=Iu(n)),s&&(r+=ku(s)))}return r},"dD");function $s(t,e,u){return String(t).normalize().replace(/\r\n/g,`
`).split(`
`).map(r=>Bs(r,e,u)).join(`
`)}a($s,"T$1");var Mu=a(t=>Array.from({length:t}).fill(""),"X");function Ts(t,e){let u=[],r=0;for(let n of t){let s=0,i=n.map(o=>{var c;let f=(c=e[r])!=null?c:"";r+=1,o.preprocess&&(f=o.preprocess(f)),Bu(f)>o.width&&(f=$s(f,o.width,{hard:!0}));let h=f.split(`
`);if(o.postprocess){let{postprocess:l}=o;h=h.map((p,C)=>l.call(o,p,C))}return o.paddingTop&&h.unshift(...Mu(o.paddingTop)),o.paddingBottom&&h.push(...Mu(o.paddingBottom)),h.length>s&&(s=h.length),ht(We({},o),{lines:h})}),D=[];for(let o=0;o<s;o+=1){let c=i.map(f=>{var h;let l=(h=f.lines[o])!=null?h:"",p=Number.isFinite(f.width)?" ".repeat(f.width-ae(l)):"",C=f.paddingLeftString;return f.align==="right"&&(C+=p),C+=l,f.align==="left"&&(C+=p),C+f.paddingRightString}).join("");D.push(c)}u.push(D.join(`
`))}return u.join(`
`)}a(Ts,"P");function xs(t,e){if(!t||t.length===0)return"";let u=Cs(t),r=u.length;if(r===0)return"";let{stdoutColumns:n,columns:s}=hs(e);if(s.length>r)throw new Error(`${s.length} columns defined, but only ${r} columns found`);let i=_s(n,s,u);return t.map(D=>Ts(i,D)).join(`
`)}a(xs,"mD"),K();var Os=["<",">","=",">=","<="];function Ns(t){if(!Os.includes(t))throw new TypeError(`Invalid breakpoint operator: ${t}`)}a(Ns,"xD");function Hs(t){let e=Object.keys(t).map(u=>{let[r,n]=u.split(" ");Ns(r);let s=Number.parseInt(n,10);if(Number.isNaN(s))throw new TypeError(`Invalid breakpoint value: ${n}`);let i=t[u];return{operator:r,breakpoint:s,value:i}}).sort((u,r)=>r.breakpoint-u.breakpoint);return u=>{var r;return(r=e.find(({operator:n,breakpoint:s})=>n==="="&&u===s||n===">"&&u>s||n==="<"&&u<s||n===">="&&u>=s||n==="<="&&u<=s))==null?void 0:r.value}}a(Hs,"wD");const Ps=a(t=>t.replace(/[\W_]([a-z\d])?/gi,(e,u)=>u?u.toUpperCase():""),"S"),Ls=a(t=>t.replace(/\B([A-Z])/g,"-$1").toLowerCase(),"q"),Is={"> 80":[{width:"content-width",paddingLeft:2,paddingRight:8},{width:"auto"}],"> 40":[{width:"auto",paddingLeft:2,paddingRight:8,preprocess:a(t=>t.trim(),"preprocess")},{width:"100%",paddingLeft:2,paddingBottom:1}],"> 0":{stdoutColumns:1e3,columns:[{width:"content-width",paddingLeft:2,paddingRight:8},{width:"content-width"}]}};function ks(t){let e=!1;return{type:"table",data:{tableData:Object.keys(t).sort((u,r)=>u.localeCompare(r)).map(u=>{const r=t[u],n="alias"in r;return n&&(e=!0),{name:u,flag:r,flagFormatted:`--${Ls(u)}`,aliasesEnabled:e,aliasFormatted:n?`-${r.alias}`:void 0}}).map(u=>(u.aliasesEnabled=e,[{type:"flagName",data:u},{type:"flagDescription",data:u}])),tableBreakpoints:Is}}}a(ks,"D");const Gu=a(t=>!t||(t.version??(t.help?t.help.version:void 0)),"A"),Wu=a(t=>{const e="parent"in t&&t.parent?.name;return(e?`${e} `:"")+t.name},"C");function Ms(t){const e=[];t.name&&e.push(Wu(t));const u=Gu(t)??("parent"in t&&Gu(t.parent));if(u&&e.push(`v${u}`),e.length!==0)return{id:"name",type:"text",data:`${e.join(" ")}
`}}a(Ms,"R");function Gs(t){const{help:e}=t;if(!(!e||!e.description))return{id:"description",type:"text",data:`${e.description}
`}}a(Gs,"L");function Ws(t){const e=t.help||{};if("usage"in e)return e.usage?{id:"usage",type:"section",data:{title:"Usage:",body:Array.isArray(e.usage)?e.usage.join(`
`):e.usage}}:void 0;if(t.name){const u=[],r=[Wu(t)];if(t.flags&&Object.keys(t.flags).length>0&&r.push("[flags...]"),t.parameters&&t.parameters.length>0){const{parameters:n}=t,s=n.indexOf("--"),i=s>-1&&n.slice(s+1).some(D=>D.startsWith("<"));r.push(n.map(D=>D!=="--"?D:i?"--":"[--]").join(" "))}if(r.length>1&&u.push(r.join(" ")),"commands"in t&&t.commands?.length&&u.push(`${t.name} <command>`),u.length>0)return{id:"usage",type:"section",data:{title:"Usage:",body:u.join(`
`)}}}}a(Ws,"T");function js(t){return!("commands"in t)||!t.commands?.length?void 0:{id:"commands",type:"section",data:{title:"Commands:",body:{type:"table",data:{tableData:t.commands.map(e=>[e.options.name,e.options.help?e.options.help.description:""]),tableOptions:[{width:"content-width",paddingLeft:2,paddingRight:8}]}},indentBody:0}}}a(js,"_");function Us(t){if(!(!t.flags||Object.keys(t.flags).length===0))return{id:"flags",type:"section",data:{title:"Flags:",body:ks(t.flags),indentBody:0}}}a(Us,"k");function Ks(t){const{help:e}=t;if(!e||!e.examples||e.examples.length===0)return;let{examples:u}=e;if(Array.isArray(u)&&(u=u.join(`
`)),u)return{id:"examples",type:"section",data:{title:"Examples:",body:u}}}a(Ks,"F");function Vs(t){if(!("alias"in t)||!t.alias)return;const{alias:e}=t;return{id:"aliases",type:"section",data:{title:"Aliases:",body:Array.isArray(e)?e.join(", "):e}}}a(Vs,"H");const zs=a(t=>[Ms,Gs,Ws,js,Us,Ks,Vs].map(e=>e(t)).filter(Boolean),"U"),Ys=vn.WriteStream.prototype.hasColors();class qs{static{a(this,"M")}text(e){return e}bold(e){return Ys?`\x1B[1m${e}\x1B[22m`:e.toLocaleUpperCase()}indentText({text:e,spaces:u}){return e.replace(/^/gm," ".repeat(u))}heading(e){return this.bold(e)}section({title:e,body:u,indentBody:r=2}){return`${(e?`${this.heading(e)}
`:"")+(u?this.indentText({text:this.render(u),spaces:r}):"")}
`}table({tableData:e,tableOptions:u,tableBreakpoints:r}){return xs(e.map(n=>n.map(s=>this.render(s))),r?Hs(r):u)}flagParameter(e){return e===Boolean?"":e===String?"<string>":e===Number?"<number>":Array.isArray(e)?this.flagParameter(e[0]):"<value>"}flagOperator(e){return" "}flagName(e){const{flag:u,flagFormatted:r,aliasesEnabled:n,aliasFormatted:s}=e;let i="";if(s?i+=`${s}, `:n&&(i+="    "),i+=r,"placeholder"in u&&typeof u.placeholder=="string")i+=`${this.flagOperator(e)}${u.placeholder}`;else{const D=this.flagParameter("type"in u?u.type:u);D&&(i+=`${this.flagOperator(e)}${D}`)}return i}flagDefault(e){return JSON.stringify(e)}flagDescription({flag:e}){let u="description"in e?e.description??"":"";if("default"in e){let{default:r}=e;typeof r=="function"&&(r=r()),r&&(u+=` (default: ${this.flagDefault(r)})`)}return u}render(e){if(typeof e=="string")return e;if(Array.isArray(e))return e.map(u=>this.render(u)).join(`
`);if("type"in e&&this[e.type]){const u=this[e.type];if(typeof u=="function")return u.call(this,e.data)}throw new Error(`Invalid node type: ${JSON.stringify(e)}`)}}const Ft=/^[\w.-]+$/,{stringify:ee}=JSON,Xs=/[|\\{}()[\]^$+*?.]/;function gt(t){const e=[];let u,r;for(const n of t){if(r)throw new Error(`Invalid parameter: Spread parameter ${ee(r)} must be last`);const s=n[0],i=n[n.length-1];let D;if(s==="<"&&i===">"&&(D=!0,u))throw new Error(`Invalid parameter: Required parameter ${ee(n)} cannot come after optional parameter ${ee(u)}`);if(s==="["&&i==="]"&&(D=!1,u=n),D===void 0)throw new Error(`Invalid parameter: ${ee(n)}. Must be wrapped in <> (required parameter) or [] (optional parameter)`);let o=n.slice(1,-1);const c=o.slice(-3)==="...";c&&(r=n,o=o.slice(0,-3));const f=o.match(Xs);if(f)throw new Error(`Invalid parameter: ${ee(n)}. Invalid character found ${ee(f[0])}`);e.push({name:o,required:D,spread:c})}return e}a(gt,"w");function mt(t,e,u,r){for(let n=0;n<e.length;n+=1){const{name:s,required:i,spread:D}=e[n],o=Ps(s);if(o in t)throw new Error(`Invalid parameter: ${ee(s)} is used more than once.`);const c=D?u.slice(n):u[n];if(D&&(n=e.length),i&&(!c||D&&c.length===0))return console.error(`Error: Missing required parameter ${ee(s)}
`),r(),process.exit(1);t[o]=c}}a(mt,"b");function Qs(t){return t===void 0||t!==!1}a(Qs,"W");function ju(t,e,u,r){const n={...e.flags},s=e.version;s&&(n.version={type:Boolean,description:"Show version"});const{help:i}=e,D=Qs(i);D&&!("help"in n)&&(n.help={type:Boolean,alias:"h",description:"Show help"});const o=wu(n,r,{ignore:e.ignoreArgv}),c=a(()=>{console.log(e.version)},"f");if(s&&o.flags.version===!0)return c(),process.exit(0);const f=new qs,h=D&&i?.render?i.render:C=>f.render(C),l=a(C=>{const g=zs({...e,...C?{help:C}:{},flags:n});console.log(h(g,f))},"u");if(D&&o.flags.help===!0)return l(),process.exit(0);if(e.parameters){let{parameters:C}=e,g=o._;const y=C.indexOf("--"),B=C.slice(y+1),H=Object.create(null);if(y>-1&&B.length>0){C=C.slice(0,y);const $=o._["--"];g=g.slice(0,-$.length||void 0),mt(H,gt(C),g,l),mt(H,gt(B),$,l)}else mt(H,gt(C),g,l);Object.assign(o._,H)}const p={...o,showVersion:c,showHelp:l};return typeof u=="function"&&u(p),{command:t,...p}}a(ju,"x");function Zs(t,e){const u=new Map;for(const r of e){const n=[r.options.name],{alias:s}=r.options;s&&(Array.isArray(s)?n.push(...s):n.push(s));for(const i of n){if(u.has(i))throw new Error(`Duplicate command name found: ${ee(i)}`);u.set(i,r)}}return u.get(t)}a(Zs,"z");function Uu(t,e,u=process.argv.slice(2)){if(!t)throw new Error("Options is required");if("name"in t&&(!t.name||!Ft.test(t.name)))throw new Error(`Invalid script name: ${ee(t.name)}`);const r=u[0];if(t.commands&&Ft.test(r)){const n=Zs(r,t.commands);if(n)return ju(n.options.name,{...n.options,parent:t},n.callback,u.slice(1))}return ju(void 0,t,e,u)}a(Uu,"Z");function Js(t,e){if(!t)throw new Error("Command options are required");const{name:u}=t;if(t.name===void 0)throw new Error("Command name is required");if(!Ft.test(u))throw new Error(`Invalid command name ${JSON.stringify(u)}. Command names must be one word.`);return{options:t,callback:e}}a(Js,"G");var ei=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function ti(t){return t&&t.__esModule&&Object.prototype.hasOwnProperty.call(t,"default")?t.default:t}a(ti,"getDefaultExportFromCjs");var de={exports:{}},_t,Ku;function ui(){if(Ku)return _t;Ku=1,_t=r,r.sync=n;var t=oe;function e(s,i){var D=i.pathExt!==void 0?i.pathExt:process.env.PATHEXT;if(!D||(D=D.split(";"),D.indexOf("")!==-1))return!0;for(var o=0;o<D.length;o++){var c=D[o].toLowerCase();if(c&&s.substr(-c.length).toLowerCase()===c)return!0}return!1}a(e,"checkPathExt");function u(s,i,D){return!s.isSymbolicLink()&&!s.isFile()?!1:e(i,D)}a(u,"checkStat");function r(s,i,D){t.stat(s,function(o,c){D(o,o?!1:u(c,s,i))})}a(r,"isexe");function n(s,i){return u(t.statSync(s),s,i)}return a(n,"sync"),_t}a(ui,"requireWindows");var At,Vu;function ri(){if(Vu)return At;Vu=1,At=e,e.sync=u;var t=oe;function e(s,i,D){t.stat(s,function(o,c){D(o,o?!1:r(c,i))})}a(e,"isexe");function u(s,i){return r(t.statSync(s),i)}a(u,"sync");function r(s,i){return s.isFile()&&n(s,i)}a(r,"checkStat");function n(s,i){var D=s.mode,o=s.uid,c=s.gid,f=i.uid!==void 0?i.uid:process.getuid&&process.getuid(),h=i.gid!==void 0?i.gid:process.getgid&&process.getgid(),l=parseInt("100",8),p=parseInt("010",8),C=parseInt("001",8),g=l|p,y=D&C||D&p&&c===h||D&l&&o===f||D&g&&f===0;return y}return a(n,"checkMode"),At}a(ri,"requireMode");var Ue;process.platform==="win32"||ei.TESTING_WINDOWS?Ue=ui():Ue=ri();var ni=yt;yt.sync=si;function yt(t,e,u){if(typeof e=="function"&&(u=e,e={}),!u){if(typeof Promise!="function")throw new TypeError("callback not provided");return new Promise(function(r,n){yt(t,e||{},function(s,i){s?n(s):r(i)})})}Ue(t,e||{},function(r,n){r&&(r.code==="EACCES"||e&&e.ignoreErrors)&&(r=null,n=!1),u(r,n)})}a(yt,"isexe$1");function si(t,e){try{return Ue.sync(t,e||{})}catch(u){if(e&&e.ignoreErrors||u.code==="EACCES")return!1;throw u}}a(si,"sync");const Ee=process.platform==="win32"||process.env.OSTYPE==="cygwin"||process.env.OSTYPE==="msys",zu=z,ii=Ee?";":":",Yu=ni,qu=a(t=>Object.assign(new Error(`not found: ${t}`),{code:"ENOENT"}),"getNotFoundError"),Xu=a((t,e)=>{const u=e.colon||ii,r=t.match(/\//)||Ee&&t.match(/\\/)?[""]:[...Ee?[process.cwd()]:[],...(e.path||process.env.PATH||"").split(u)],n=Ee?e.pathExt||process.env.PATHEXT||".EXE;.CMD;.BAT;.COM":"",s=Ee?n.split(u):[""];return Ee&&t.indexOf(".")!==-1&&s[0]!==""&&s.unshift(""),{pathEnv:r,pathExt:s,pathExtExe:n}},"getPathInfo"),Qu=a((t,e,u)=>{typeof e=="function"&&(u=e,e={}),e||(e={});const{pathEnv:r,pathExt:n,pathExtExe:s}=Xu(t,e),i=[],D=a(c=>new Promise((f,h)=>{if(c===r.length)return e.all&&i.length?f(i):h(qu(t));const l=r[c],p=/^".*"$/.test(l)?l.slice(1,-1):l,C=zu.join(p,t),g=!p&&/^\.[\\\/]/.test(t)?t.slice(0,2)+C:C;f(o(g,c,0))}),"step"),o=a((c,f,h)=>new Promise((l,p)=>{if(h===n.length)return l(D(f+1));const C=n[h];Yu(c+C,{pathExt:s},(g,y)=>{if(!g&&y)if(e.all)i.push(c+C);else return l(c+C);return l(o(c,f,h+1))})}),"subStep");return u?D(0).then(c=>u(null,c),u):D(0)},"which$1"),Di=a((t,e)=>{e=e||{};const{pathEnv:u,pathExt:r,pathExtExe:n}=Xu(t,e),s=[];for(let i=0;i<u.length;i++){const D=u[i],o=/^".*"$/.test(D)?D.slice(1,-1):D,c=zu.join(o,t),f=!o&&/^\.[\\\/]/.test(t)?t.slice(0,2)+c:c;for(let h=0;h<r.length;h++){const l=f+r[h];try{if(Yu.sync(l,{pathExt:n}))if(e.all)s.push(l);else return l}catch{}}}if(e.all&&s.length)return s;if(e.nothrow)return null;throw qu(t)},"whichSync");var oi=Qu;Qu.sync=Di;var wt={exports:{}};const Zu=a((t={})=>{const e=t.env||process.env;return(t.platform||process.platform)!=="win32"?"PATH":Object.keys(e).reverse().find(r=>r.toUpperCase()==="PATH")||"Path"},"pathKey");wt.exports=Zu,wt.exports.default=Zu;var ai=wt.exports;const Ju=z,li=oi,ci=ai;function er(t,e){const u=t.options.env||process.env,r=process.cwd(),n=t.options.cwd!=null,s=n&&process.chdir!==void 0&&!process.chdir.disabled;if(s)try{process.chdir(t.options.cwd)}catch{}let i;try{i=li.sync(t.command,{path:u[ci({env:u})],pathExt:e?Ju.delimiter:void 0})}catch{}finally{s&&process.chdir(r)}return i&&(i=Ju.resolve(n?t.options.cwd:"",i)),i}a(er,"resolveCommandAttempt");function fi(t){return er(t)||er(t,!0)}a(fi,"resolveCommand$1");var hi=fi,Rt={};const bt=/([()\][%!^"`<>&|;, *?])/g;function di(t){return t=t.replace(bt,"^$1"),t}a(di,"escapeCommand");function Ei(t,e){return t=`${t}`,t=t.replace(/(\\*)"/g,'$1$1\\"'),t=t.replace(/(\\*)$/,"$1$1"),t=`"${t}"`,t=t.replace(bt,"^$1"),e&&(t=t.replace(bt,"^$1")),t}a(Ei,"escapeArgument"),Rt.command=di,Rt.argument=Ei;var pi=/^#!(.*)/;const Ci=pi;var Fi=a((t="")=>{const e=t.match(Ci);if(!e)return null;const[u,r]=e[0].replace(/#! ?/,"").split(" "),n=u.split("/").pop();return n==="env"?r:r?`${n} ${r}`:n},"shebangCommand$1");const vt=oe,gi=Fi;function mi(t){const u=Buffer.alloc(150);let r;try{r=vt.openSync(t,"r"),vt.readSync(r,u,0,150,0),vt.closeSync(r)}catch{}return gi(u.toString())}a(mi,"readShebang$1");var _i=mi;const Ai=z,tr=hi,ur=Rt,yi=_i,wi=process.platform==="win32",Ri=/\.(?:com|exe)$/i,bi=/node_modules[\\/].bin[\\/][^\\/]+\.cmd$/i;function vi(t){t.file=tr(t);const e=t.file&&yi(t.file);return e?(t.args.unshift(t.file),t.command=e,tr(t)):t.file}a(vi,"detectShebang");function Si(t){if(!wi)return t;const e=vi(t),u=!Ri.test(e);if(t.options.forceShell||u){const r=bi.test(e);t.command=Ai.normalize(t.command),t.command=ur.command(t.command),t.args=t.args.map(s=>ur.argument(s,r));const n=[t.command].concat(t.args).join(" ");t.args=["/d","/s","/c",`"${n}"`],t.command=process.env.comspec||"cmd.exe",t.options.windowsVerbatimArguments=!0}return t}a(Si,"parseNonShell");function Bi(t,e,u){e&&!Array.isArray(e)&&(u=e,e=null),e=e?e.slice(0):[],u=Object.assign({},u);const r={command:t,args:e,options:u,file:void 0,original:{command:t,args:e}};return u.shell?r:Si(r)}a(Bi,"parse$5");var $i=Bi;const St=process.platform==="win32";function Bt(t,e){return Object.assign(new Error(`${e} ${t.command} ENOENT`),{code:"ENOENT",errno:"ENOENT",syscall:`${e} ${t.command}`,path:t.command,spawnargs:t.args})}a(Bt,"notFoundError");function Ti(t,e){if(!St)return;const u=t.emit;t.emit=function(r,n){if(r==="exit"){const s=rr(n,e);if(s)return u.call(t,"error",s)}return u.apply(t,arguments)}}a(Ti,"hookChildProcess");function rr(t,e){return St&&t===1&&!e.file?Bt(e.original,"spawn"):null}a(rr,"verifyENOENT");function xi(t,e){return St&&t===1&&!e.file?Bt(e.original,"spawnSync"):null}a(xi,"verifyENOENTSync");var Oi={hookChildProcess:Ti,verifyENOENT:rr,verifyENOENTSync:xi,notFoundError:Bt};const nr=$n,$t=$i,Tt=Oi;function sr(t,e,u){const r=$t(t,e,u),n=nr.spawn(r.command,r.args,r.options);return Tt.hookChildProcess(n,r),n}a(sr,"spawn");function Ni(t,e,u){const r=$t(t,e,u),n=nr.spawnSync(r.command,r.args,r.options);return n.error=n.error||Tt.verifyENOENTSync(n.status,r),n}a(Ni,"spawnSync"),de.exports=sr,de.exports.spawn=sr,de.exports.sync=Ni,de.exports._parse=$t,de.exports._enoent=Tt;var Hi=de.exports,Pi=ti(Hi);const ir=a((t,e)=>{const u={...process.env},r=["inherit","inherit","inherit"];process.send&&r.push("ipc"),e&&(e.noCache&&(u.TSX_DISABLE_CACHE="1"),e.tsconfigPath&&(u.TSX_TSCONFIG_PATH=e.tsconfigPath));const n=t.filter(s=>s!=="-i"&&s!=="--interactive").length===0;return Pi(process.execPath,["--require",he.require.resolve("./preflight.cjs"),...n?["--require",he.require.resolve("./patch-repl.cjs")]:[],ke.isFeatureSupported(ke.moduleRegister)?"--import":"--loader",_u.pathToFileURL(he.require.resolve("./loader.mjs")).toString(),...t],{stdio:r,env:u})},"run");var Ke={};const Li=z,te="\\\\/",Dr=`[^${te}]`,re="\\.",Ii="\\+",ki="\\?",Ve="\\/",Mi="(?=.)",or="[^/]",xt=`(?:${Ve}|$)`,ar=`(?:^|${Ve})`,Ot=`${re}{1,2}${xt}`,Gi=`(?!${re})`,Wi=`(?!${ar}${Ot})`,ji=`(?!${re}{0,1}${xt})`,Ui=`(?!${Ot})`,Ki=`[^.${Ve}]`,Vi=`${or}*?`,lr={DOT_LITERAL:re,PLUS_LITERAL:Ii,QMARK_LITERAL:ki,SLASH_LITERAL:Ve,ONE_CHAR:Mi,QMARK:or,END_ANCHOR:xt,DOTS_SLASH:Ot,NO_DOT:Gi,NO_DOTS:Wi,NO_DOT_SLASH:ji,NO_DOTS_SLASH:Ui,QMARK_NO_DOT:Ki,STAR:Vi,START_ANCHOR:ar},zi={...lr,SLASH_LITERAL:`[${te}]`,QMARK:Dr,STAR:`${Dr}*?`,DOTS_SLASH:`${re}{1,2}(?:[${te}]|$)`,NO_DOT:`(?!${re})`,NO_DOTS:`(?!(?:^|[${te}])${re}{1,2}(?:[${te}]|$))`,NO_DOT_SLASH:`(?!${re}{0,1}(?:[${te}]|$))`,NO_DOTS_SLASH:`(?!${re}{1,2}(?:[${te}]|$))`,QMARK_NO_DOT:`[^.${te}]`,START_ANCHOR:`(?:^|[${te}])`,END_ANCHOR:`(?:[${te}]|$)`},Yi={alnum:"a-zA-Z0-9",alpha:"a-zA-Z",ascii:"\\x00-\\x7F",blank:" \\t",cntrl:"\\x00-\\x1F\\x7F",digit:"0-9",graph:"\\x21-\\x7E",lower:"a-z",print:"\\x20-\\x7E ",punct:"\\-!\"#$%&'()\\*+,./:;<=>?@[\\]^_`{|}~",space:" \\t\\r\\n\\v\\f",upper:"A-Z",word:"A-Za-z0-9_",xdigit:"A-Fa-f0-9"};var ze={MAX_LENGTH:1024*64,POSIX_REGEX_SOURCE:Yi,REGEX_BACKSLASH:/\\(?![*+?^${}(|)[\]])/g,REGEX_NON_SPECIAL_CHARS:/^[^@![\].,$*+?^{}()|\\/]+/,REGEX_SPECIAL_CHARS:/[-*+?.^${}(|)[\]]/,REGEX_SPECIAL_CHARS_BACKREF:/(\\?)((\W)(\3*))/g,REGEX_SPECIAL_CHARS_GLOBAL:/([-*+?.^${}(|)[\]])/g,REGEX_REMOVE_BACKSLASH:/(?:\[.*?[^\\]\]|\\(?=.))/g,REPLACEMENTS:{"***":"*","**/**":"**","**/**/**":"**"},CHAR_0:48,CHAR_9:57,CHAR_UPPERCASE_A:65,CHAR_LOWERCASE_A:97,CHAR_UPPERCASE_Z:90,CHAR_LOWERCASE_Z:122,CHAR_LEFT_PARENTHESES:40,CHAR_RIGHT_PARENTHESES:41,CHAR_ASTERISK:42,CHAR_AMPERSAND:38,CHAR_AT:64,CHAR_BACKWARD_SLASH:92,CHAR_CARRIAGE_RETURN:13,CHAR_CIRCUMFLEX_ACCENT:94,CHAR_COLON:58,CHAR_COMMA:44,CHAR_DOT:46,CHAR_DOUBLE_QUOTE:34,CHAR_EQUAL:61,CHAR_EXCLAMATION_MARK:33,CHAR_FORM_FEED:12,CHAR_FORWARD_SLASH:47,CHAR_GRAVE_ACCENT:96,CHAR_HASH:35,CHAR_HYPHEN_MINUS:45,CHAR_LEFT_ANGLE_BRACKET:60,CHAR_LEFT_CURLY_BRACE:123,CHAR_LEFT_SQUARE_BRACKET:91,CHAR_LINE_FEED:10,CHAR_NO_BREAK_SPACE:160,CHAR_PERCENT:37,CHAR_PLUS:43,CHAR_QUESTION_MARK:63,CHAR_RIGHT_ANGLE_BRACKET:62,CHAR_RIGHT_CURLY_BRACE:125,CHAR_RIGHT_SQUARE_BRACKET:93,CHAR_SEMICOLON:59,CHAR_SINGLE_QUOTE:39,CHAR_SPACE:32,CHAR_TAB:9,CHAR_UNDERSCORE:95,CHAR_VERTICAL_LINE:124,CHAR_ZERO_WIDTH_NOBREAK_SPACE:65279,SEP:Li.sep,extglobChars(t){return{"!":{type:"negate",open:"(?:(?!(?:",close:`))${t.STAR})`},"?":{type:"qmark",open:"(?:",close:")?"},"+":{type:"plus",open:"(?:",close:")+"},"*":{type:"star",open:"(?:",close:")*"},"@":{type:"at",open:"(?:",close:")"}}},globChars(t){return t===!0?zi:lr}};(function(t){const e=z,u=process.platform==="win32",{REGEX_BACKSLASH:r,REGEX_REMOVE_BACKSLASH:n,REGEX_SPECIAL_CHARS:s,REGEX_SPECIAL_CHARS_GLOBAL:i}=ze;t.isObject=D=>D!==null&&typeof D=="object"&&!Array.isArray(D),t.hasRegexChars=D=>s.test(D),t.isRegexChar=D=>D.length===1&&t.hasRegexChars(D),t.escapeRegex=D=>D.replace(i,"\\$1"),t.toPosixSlashes=D=>D.replace(r,"/"),t.removeBackslashes=D=>D.replace(n,o=>o==="\\"?"":o),t.supportsLookbehinds=()=>{const D=process.version.slice(1).split(".").map(Number);return D.length===3&&D[0]>=9||D[0]===8&&D[1]>=10},t.isWindows=D=>D&&typeof D.windows=="boolean"?D.windows:u===!0||e.sep==="\\",t.escapeLast=(D,o,c)=>{const f=D.lastIndexOf(o,c);return f===-1?D:D[f-1]==="\\"?t.escapeLast(D,o,f-1):`${D.slice(0,f)}\\${D.slice(f)}`},t.removePrefix=(D,o={})=>{let c=D;return c.startsWith("./")&&(c=c.slice(2),o.prefix="./"),c},t.wrapOutput=(D,o={},c={})=>{const f=c.contains?"":"^",h=c.contains?"":"$";let l=`${f}(?:${D})${h}`;return o.negated===!0&&(l=`(?:^(?!${l}).*$)`),l}})(Ke);const cr=Ke,{CHAR_ASTERISK:Nt,CHAR_AT:qi,CHAR_BACKWARD_SLASH:we,CHAR_COMMA:Xi,CHAR_DOT:Ht,CHAR_EXCLAMATION_MARK:Pt,CHAR_FORWARD_SLASH:fr,CHAR_LEFT_CURLY_BRACE:Lt,CHAR_LEFT_PARENTHESES:It,CHAR_LEFT_SQUARE_BRACKET:Qi,CHAR_PLUS:Zi,CHAR_QUESTION_MARK:hr,CHAR_RIGHT_CURLY_BRACE:Ji,CHAR_RIGHT_PARENTHESES:dr,CHAR_RIGHT_SQUARE_BRACKET:eD}=ze,Er=a(t=>t===fr||t===we,"isPathSeparator"),pr=a(t=>{t.isPrefix!==!0&&(t.depth=t.isGlobstar?1/0:1)},"depth"),tD=a((t,e)=>{const u=e||{},r=t.length-1,n=u.parts===!0||u.scanToEnd===!0,s=[],i=[],D=[];let o=t,c=-1,f=0,h=0,l=!1,p=!1,C=!1,g=!1,y=!1,B=!1,H=!1,$=!1,Q=!1,G=!1,se=0,W,A,v={value:"",depth:0,isGlob:!1};const M=a(()=>c>=r,"eos"),F=a(()=>o.charCodeAt(c+1),"peek"),O=a(()=>(W=A,o.charCodeAt(++c)),"advance");for(;c<r;){A=O();let j;if(A===we){H=v.backslashes=!0,A=O(),A===Lt&&(B=!0);continue}if(B===!0||A===Lt){for(se++;M()!==!0&&(A=O());){if(A===we){H=v.backslashes=!0,O();continue}if(A===Lt){se++;continue}if(B!==!0&&A===Ht&&(A=O())===Ht){if(l=v.isBrace=!0,C=v.isGlob=!0,G=!0,n===!0)continue;break}if(B!==!0&&A===Xi){if(l=v.isBrace=!0,C=v.isGlob=!0,G=!0,n===!0)continue;break}if(A===Ji&&(se--,se===0)){B=!1,l=v.isBrace=!0,G=!0;break}}if(n===!0)continue;break}if(A===fr){if(s.push(c),i.push(v),v={value:"",depth:0,isGlob:!1},G===!0)continue;if(W===Ht&&c===f+1){f+=2;continue}h=c+1;continue}if(u.noext!==!0&&(A===Zi||A===qi||A===Nt||A===hr||A===Pt)===!0&&F()===It){if(C=v.isGlob=!0,g=v.isExtglob=!0,G=!0,A===Pt&&c===f&&(Q=!0),n===!0){for(;M()!==!0&&(A=O());){if(A===we){H=v.backslashes=!0,A=O();continue}if(A===dr){C=v.isGlob=!0,G=!0;break}}continue}break}if(A===Nt){if(W===Nt&&(y=v.isGlobstar=!0),C=v.isGlob=!0,G=!0,n===!0)continue;break}if(A===hr){if(C=v.isGlob=!0,G=!0,n===!0)continue;break}if(A===Qi){for(;M()!==!0&&(j=O());){if(j===we){H=v.backslashes=!0,O();continue}if(j===eD){p=v.isBracket=!0,C=v.isGlob=!0,G=!0;break}}if(n===!0)continue;break}if(u.nonegate!==!0&&A===Pt&&c===f){$=v.negated=!0,f++;continue}if(u.noparen!==!0&&A===It){if(C=v.isGlob=!0,n===!0){for(;M()!==!0&&(A=O());){if(A===It){H=v.backslashes=!0,A=O();continue}if(A===dr){G=!0;break}}continue}break}if(C===!0){if(G=!0,n===!0)continue;break}}u.noext===!0&&(g=!1,C=!1);let T=o,ie="",d="";f>0&&(ie=o.slice(0,f),o=o.slice(f),h-=f),T&&C===!0&&h>0?(T=o.slice(0,h),d=o.slice(h)):C===!0?(T="",d=o):T=o,T&&T!==""&&T!=="/"&&T!==o&&Er(T.charCodeAt(T.length-1))&&(T=T.slice(0,-1)),u.unescape===!0&&(d&&(d=cr.removeBackslashes(d)),T&&H===!0&&(T=cr.removeBackslashes(T)));const E={prefix:ie,input:t,start:f,base:T,glob:d,isBrace:l,isBracket:p,isGlob:C,isExtglob:g,isGlobstar:y,negated:$,negatedExtglob:Q};if(u.tokens===!0&&(E.maxDepth=0,Er(A)||i.push(v),E.tokens=i),u.parts===!0||u.tokens===!0){let j;for(let b=0;b<s.length;b++){const Z=j?j+1:f,J=s[b],V=t.slice(Z,J);u.tokens&&(b===0&&f!==0?(i[b].isPrefix=!0,i[b].value=ie):i[b].value=V,pr(i[b]),E.maxDepth+=i[b].depth),(b!==0||V!=="")&&D.push(V),j=J}if(j&&j+1<t.length){const b=t.slice(j+1);D.push(b),u.tokens&&(i[i.length-1].value=b,pr(i[i.length-1]),E.maxDepth+=i[i.length-1].depth)}E.slashes=s,E.parts=D}return E},"scan$1");var uD=tD;const Ye=ze,Y=Ke,{MAX_LENGTH:qe,POSIX_REGEX_SOURCE:rD,REGEX_NON_SPECIAL_CHARS:nD,REGEX_SPECIAL_CHARS_BACKREF:sD,REPLACEMENTS:Cr}=Ye,iD=a((t,e)=>{if(typeof e.expandRange=="function")return e.expandRange(...t,e);t.sort();const u=`[${t.join("-")}]`;try{new RegExp(u)}catch{return t.map(n=>Y.escapeRegex(n)).join("..")}return u},"expandRange"),pe=a((t,e)=>`Missing ${t}: "${e}" - use "\\\\${e}" to match literal characters`,"syntaxError"),kt=a((t,e)=>{if(typeof t!="string")throw new TypeError("Expected a string");t=Cr[t]||t;const u={...e},r=typeof u.maxLength=="number"?Math.min(qe,u.maxLength):qe;let n=t.length;if(n>r)throw new SyntaxError(`Input length: ${n}, exceeds maximum allowed length: ${r}`);const s={type:"bos",value:"",output:u.prepend||""},i=[s],D=u.capture?"":"?:",o=Y.isWindows(e),c=Ye.globChars(o),f=Ye.extglobChars(c),{DOT_LITERAL:h,PLUS_LITERAL:l,SLASH_LITERAL:p,ONE_CHAR:C,DOTS_SLASH:g,NO_DOT:y,NO_DOT_SLASH:B,NO_DOTS_SLASH:H,QMARK:$,QMARK_NO_DOT:Q,STAR:G,START_ANCHOR:se}=c,W=a(_=>`(${D}(?:(?!${se}${_.dot?g:h}).)*?)`,"globstar"),A=u.dot?"":y,v=u.dot?$:Q;let M=u.bash===!0?W(u):G;u.capture&&(M=`(${M})`),typeof u.noext=="boolean"&&(u.noextglob=u.noext);const F={input:t,index:-1,start:0,dot:u.dot===!0,consumed:"",output:"",prefix:"",backtrack:!1,negated:!1,brackets:0,braces:0,parens:0,quotes:0,globstar:!1,tokens:i};t=Y.removePrefix(t,F),n=t.length;const O=[],T=[],ie=[];let d=s,E;const j=a(()=>F.index===n-1,"eos"),b=F.peek=(_=1)=>t[F.index+_],Z=F.advance=()=>t[++F.index]||"",J=a(()=>t.slice(F.index+1),"remaining"),V=a((_="",x=0)=>{F.consumed+=_,F.index+=x},"consume"),He=a(_=>{F.output+=_.output!=null?_.output:_.value,V(_.value)},"append"),wn=a(()=>{let _=1;for(;b()==="!"&&(b(2)!=="("||b(3)==="?");)Z(),F.start++,_++;return _%2===0?!1:(F.negated=!0,F.start++,!0)},"negate"),Pe=a(_=>{F[_]++,ie.push(_)},"increment"),De=a(_=>{F[_]--,ie.pop()},"decrement"),R=a(_=>{if(d.type==="globstar"){const x=F.braces>0&&(_.type==="comma"||_.type==="brace"),m=_.extglob===!0||O.length&&(_.type==="pipe"||_.type==="paren");_.type!=="slash"&&_.type!=="paren"&&!x&&!m&&(F.output=F.output.slice(0,-d.output.length),d.type="star",d.value="*",d.output=M,F.output+=d.output)}if(O.length&&_.type!=="paren"&&(O[O.length-1].inner+=_.value),(_.value||_.output)&&He(_),d&&d.type==="text"&&_.type==="text"){d.value+=_.value,d.output=(d.output||"")+_.value;return}_.prev=d,i.push(_),d=_},"push"),Le=a((_,x)=>{const m={...f[x],conditions:1,inner:""};m.prev=d,m.parens=F.parens,m.output=F.output;const w=(u.capture?"(":"")+m.open;Pe("parens"),R({type:_,value:x,output:F.output?"":C}),R({type:"paren",extglob:!0,value:Z(),output:w}),O.push(m)},"extglobOpen"),Rn=a(_=>{let x=_.close+(u.capture?")":""),m;if(_.type==="negate"){let w=M;if(_.inner&&_.inner.length>1&&_.inner.includes("/")&&(w=W(u)),(w!==M||j()||/^\)+$/.test(J()))&&(x=_.close=`)$))${w}`),_.inner.includes("*")&&(m=J())&&/^\.[^\\/.]+$/.test(m)){const N=kt(m,{...e,fastpaths:!1}).output;x=_.close=`)${N})${w})`}_.prev.type==="bos"&&(F.negatedExtglob=!0)}R({type:"paren",extglob:!0,value:E,output:x}),De("parens")},"extglobClose");if(u.fastpaths!==!1&&!/(^[*!]|[/()[\]{}"])/.test(t)){let _=!1,x=t.replace(sD,(m,w,N,U,L,lt)=>U==="\\"?(_=!0,m):U==="?"?w?w+U+(L?$.repeat(L.length):""):lt===0?v+(L?$.repeat(L.length):""):$.repeat(N.length):U==="."?h.repeat(N.length):U==="*"?w?w+U+(L?M:""):M:w?m:`\\${m}`);return _===!0&&(u.unescape===!0?x=x.replace(/\\/g,""):x=x.replace(/\\+/g,m=>m.length%2===0?"\\\\":m?"\\":"")),x===t&&u.contains===!0?(F.output=t,F):(F.output=Y.wrapOutput(x,F,e),F)}for(;!j();){if(E=Z(),E==="\0")continue;if(E==="\\"){const m=b();if(m==="/"&&u.bash!==!0||m==="."||m===";")continue;if(!m){E+="\\",R({type:"text",value:E});continue}const w=/^\\+/.exec(J());let N=0;if(w&&w[0].length>2&&(N=w[0].length,F.index+=N,N%2!==0&&(E+="\\")),u.unescape===!0?E=Z():E+=Z(),F.brackets===0){R({type:"text",value:E});continue}}if(F.brackets>0&&(E!=="]"||d.value==="["||d.value==="[^")){if(u.posix!==!1&&E===":"){const m=d.value.slice(1);if(m.includes("[")&&(d.posix=!0,m.includes(":"))){const w=d.value.lastIndexOf("["),N=d.value.slice(0,w),U=d.value.slice(w+2),L=rD[U];if(L){d.value=N+L,F.backtrack=!0,Z(),!s.output&&i.indexOf(d)===1&&(s.output=C);continue}}}(E==="["&&b()!==":"||E==="-"&&b()==="]")&&(E=`\\${E}`),E==="]"&&(d.value==="["||d.value==="[^")&&(E=`\\${E}`),u.posix===!0&&E==="!"&&d.value==="["&&(E="^"),d.value+=E,He({value:E});continue}if(F.quotes===1&&E!=='"'){E=Y.escapeRegex(E),d.value+=E,He({value:E});continue}if(E==='"'){F.quotes=F.quotes===1?0:1,u.keepQuotes===!0&&R({type:"text",value:E});continue}if(E==="("){Pe("parens"),R({type:"paren",value:E});continue}if(E===")"){if(F.parens===0&&u.strictBrackets===!0)throw new SyntaxError(pe("opening","("));const m=O[O.length-1];if(m&&F.parens===m.parens+1){Rn(O.pop());continue}R({type:"paren",value:E,output:F.parens?")":"\\)"}),De("parens");continue}if(E==="["){if(u.nobracket===!0||!J().includes("]")){if(u.nobracket!==!0&&u.strictBrackets===!0)throw new SyntaxError(pe("closing","]"));E=`\\${E}`}else Pe("brackets");R({type:"bracket",value:E});continue}if(E==="]"){if(u.nobracket===!0||d&&d.type==="bracket"&&d.value.length===1){R({type:"text",value:E,output:`\\${E}`});continue}if(F.brackets===0){if(u.strictBrackets===!0)throw new SyntaxError(pe("opening","["));R({type:"text",value:E,output:`\\${E}`});continue}De("brackets");const m=d.value.slice(1);if(d.posix!==!0&&m[0]==="^"&&!m.includes("/")&&(E=`/${E}`),d.value+=E,He({value:E}),u.literalBrackets===!1||Y.hasRegexChars(m))continue;const w=Y.escapeRegex(d.value);if(F.output=F.output.slice(0,-d.value.length),u.literalBrackets===!0){F.output+=w,d.value=w;continue}d.value=`(${D}${w}|${d.value})`,F.output+=d.value;continue}if(E==="{"&&u.nobrace!==!0){Pe("braces");const m={type:"brace",value:E,output:"(",outputIndex:F.output.length,tokensIndex:F.tokens.length};T.push(m),R(m);continue}if(E==="}"){const m=T[T.length-1];if(u.nobrace===!0||!m){R({type:"text",value:E,output:E});continue}let w=")";if(m.dots===!0){const N=i.slice(),U=[];for(let L=N.length-1;L>=0&&(i.pop(),N[L].type!=="brace");L--)N[L].type!=="dots"&&U.unshift(N[L].value);w=iD(U,u),F.backtrack=!0}if(m.comma!==!0&&m.dots!==!0){const N=F.output.slice(0,m.outputIndex),U=F.tokens.slice(m.tokensIndex);m.value=m.output="\\{",E=w="\\}",F.output=N;for(const L of U)F.output+=L.output||L.value}R({type:"brace",value:E,output:w}),De("braces"),T.pop();continue}if(E==="|"){O.length>0&&O[O.length-1].conditions++,R({type:"text",value:E});continue}if(E===","){let m=E;const w=T[T.length-1];w&&ie[ie.length-1]==="braces"&&(w.comma=!0,m="|"),R({type:"comma",value:E,output:m});continue}if(E==="/"){if(d.type==="dot"&&F.index===F.start+1){F.start=F.index+1,F.consumed="",F.output="",i.pop(),d=s;continue}R({type:"slash",value:E,output:p});continue}if(E==="."){if(F.braces>0&&d.type==="dot"){d.value==="."&&(d.output=h);const m=T[T.length-1];d.type="dots",d.output+=E,d.value+=E,m.dots=!0;continue}if(F.braces+F.parens===0&&d.type!=="bos"&&d.type!=="slash"){R({type:"text",value:E,output:h});continue}R({type:"dot",value:E,output:h});continue}if(E==="?"){if(!(d&&d.value==="(")&&u.noextglob!==!0&&b()==="("&&b(2)!=="?"){Le("qmark",E);continue}if(d&&d.type==="paren"){const w=b();let N=E;if(w==="<"&&!Y.supportsLookbehinds())throw new Error("Node.js v10 or higher is required for regex lookbehinds");(d.value==="("&&!/[!=<:]/.test(w)||w==="<"&&!/<([!=]|\w+>)/.test(J()))&&(N=`\\${E}`),R({type:"text",value:E,output:N});continue}if(u.dot!==!0&&(d.type==="slash"||d.type==="bos")){R({type:"qmark",value:E,output:Q});continue}R({type:"qmark",value:E,output:$});continue}if(E==="!"){if(u.noextglob!==!0&&b()==="("&&(b(2)!=="?"||!/[!=<:]/.test(b(3)))){Le("negate",E);continue}if(u.nonegate!==!0&&F.index===0){wn();continue}}if(E==="+"){if(u.noextglob!==!0&&b()==="("&&b(2)!=="?"){Le("plus",E);continue}if(d&&d.value==="("||u.regex===!1){R({type:"plus",value:E,output:l});continue}if(d&&(d.type==="bracket"||d.type==="paren"||d.type==="brace")||F.parens>0){R({type:"plus",value:E});continue}R({type:"plus",value:l});continue}if(E==="@"){if(u.noextglob!==!0&&b()==="("&&b(2)!=="?"){R({type:"at",extglob:!0,value:E,output:""});continue}R({type:"text",value:E});continue}if(E!=="*"){(E==="$"||E==="^")&&(E=`\\${E}`);const m=nD.exec(J());m&&(E+=m[0],F.index+=m[0].length),R({type:"text",value:E});continue}if(d&&(d.type==="globstar"||d.star===!0)){d.type="star",d.star=!0,d.value+=E,d.output=M,F.backtrack=!0,F.globstar=!0,V(E);continue}let _=J();if(u.noextglob!==!0&&/^\([^?]/.test(_)){Le("star",E);continue}if(d.type==="star"){if(u.noglobstar===!0){V(E);continue}const m=d.prev,w=m.prev,N=m.type==="slash"||m.type==="bos",U=w&&(w.type==="star"||w.type==="globstar");if(u.bash===!0&&(!N||_[0]&&_[0]!=="/")){R({type:"star",value:E,output:""});continue}const L=F.braces>0&&(m.type==="comma"||m.type==="brace"),lt=O.length&&(m.type==="pipe"||m.type==="paren");if(!N&&m.type!=="paren"&&!L&&!lt){R({type:"star",value:E,output:""});continue}for(;_.slice(0,3)==="/**";){const Ie=t[F.index+4];if(Ie&&Ie!=="/")break;_=_.slice(3),V("/**",3)}if(m.type==="bos"&&j()){d.type="globstar",d.value+=E,d.output=W(u),F.output=d.output,F.globstar=!0,V(E);continue}if(m.type==="slash"&&m.prev.type!=="bos"&&!U&&j()){F.output=F.output.slice(0,-(m.output+d.output).length),m.output=`(?:${m.output}`,d.type="globstar",d.output=W(u)+(u.strictSlashes?")":"|$)"),d.value+=E,F.globstar=!0,F.output+=m.output+d.output,V(E);continue}if(m.type==="slash"&&m.prev.type!=="bos"&&_[0]==="/"){const Ie=_[1]!==void 0?"|$":"";F.output=F.output.slice(0,-(m.output+d.output).length),m.output=`(?:${m.output}`,d.type="globstar",d.output=`${W(u)}${p}|${p}${Ie})`,d.value+=E,F.output+=m.output+d.output,F.globstar=!0,V(E+Z()),R({type:"slash",value:"/",output:""});continue}if(m.type==="bos"&&_[0]==="/"){d.type="globstar",d.value+=E,d.output=`(?:^|${p}|${W(u)}${p})`,F.output=d.output,F.globstar=!0,V(E+Z()),R({type:"slash",value:"/",output:""});continue}F.output=F.output.slice(0,-d.output.length),d.type="globstar",d.output=W(u),d.value+=E,F.output+=d.output,F.globstar=!0,V(E);continue}const x={type:"star",value:E,output:M};if(u.bash===!0){x.output=".*?",(d.type==="bos"||d.type==="slash")&&(x.output=A+x.output),R(x);continue}if(d&&(d.type==="bracket"||d.type==="paren")&&u.regex===!0){x.output=E,R(x);continue}(F.index===F.start||d.type==="slash"||d.type==="dot")&&(d.type==="dot"?(F.output+=B,d.output+=B):u.dot===!0?(F.output+=H,d.output+=H):(F.output+=A,d.output+=A),b()!=="*"&&(F.output+=C,d.output+=C)),R(x)}for(;F.brackets>0;){if(u.strictBrackets===!0)throw new SyntaxError(pe("closing","]"));F.output=Y.escapeLast(F.output,"["),De("brackets")}for(;F.parens>0;){if(u.strictBrackets===!0)throw new SyntaxError(pe("closing",")"));F.output=Y.escapeLast(F.output,"("),De("parens")}for(;F.braces>0;){if(u.strictBrackets===!0)throw new SyntaxError(pe("closing","}"));F.output=Y.escapeLast(F.output,"{"),De("braces")}if(u.strictSlashes!==!0&&(d.type==="star"||d.type==="bracket")&&R({type:"maybe_slash",value:"",output:`${p}?`}),F.backtrack===!0){F.output="";for(const _ of F.tokens)F.output+=_.output!=null?_.output:_.value,_.suffix&&(F.output+=_.suffix)}return F},"parse$3");kt.fastpaths=(t,e)=>{const u={...e},r=typeof u.maxLength=="number"?Math.min(qe,u.maxLength):qe,n=t.length;if(n>r)throw new SyntaxError(`Input length: ${n}, exceeds maximum allowed length: ${r}`);t=Cr[t]||t;const s=Y.isWindows(e),{DOT_LITERAL:i,SLASH_LITERAL:D,ONE_CHAR:o,DOTS_SLASH:c,NO_DOT:f,NO_DOTS:h,NO_DOTS_SLASH:l,STAR:p,START_ANCHOR:C}=Ye.globChars(s),g=u.dot?h:f,y=u.dot?l:f,B=u.capture?"":"?:",H={negated:!1,prefix:""};let $=u.bash===!0?".*?":p;u.capture&&($=`(${$})`);const Q=a(A=>A.noglobstar===!0?$:`(${B}(?:(?!${C}${A.dot?c:i}).)*?)`,"globstar"),G=a(A=>{switch(A){case"*":return`${g}${o}${$}`;case".*":return`${i}${o}${$}`;case"*.*":return`${g}${$}${i}${o}${$}`;case"*/*":return`${g}${$}${D}${o}${y}${$}`;case"**":return g+Q(u);case"**/*":return`(?:${g}${Q(u)}${D})?${y}${o}${$}`;case"**/*.*":return`(?:${g}${Q(u)}${D})?${y}${$}${i}${o}${$}`;case"**/.*":return`(?:${g}${Q(u)}${D})?${i}${o}${$}`;default:{const v=/^(.*?)\.(\w+)$/.exec(A);if(!v)return;const M=G(v[1]);return M?M+i+v[2]:void 0}}},"create"),se=Y.removePrefix(t,H);let W=G(se);return W&&u.strictSlashes!==!0&&(W+=`${D}?`),W};var DD=kt;const oD=z,aD=uD,Mt=DD,Gt=Ke,lD=ze,cD=a(t=>t&&typeof t=="object"&&!Array.isArray(t),"isObject$1"),P=a((t,e,u=!1)=>{if(Array.isArray(t)){const f=t.map(l=>P(l,e,u));return a(l=>{for(const p of f){const C=p(l);if(C)return C}return!1},"arrayMatcher")}const r=cD(t)&&t.tokens&&t.input;if(t===""||typeof t!="string"&&!r)throw new TypeError("Expected pattern to be a non-empty string");const n=e||{},s=Gt.isWindows(e),i=r?P.compileRe(t,e):P.makeRe(t,e,!1,!0),D=i.state;delete i.state;let o=a(()=>!1,"isIgnored");if(n.ignore){const f={...e,ignore:null,onMatch:null,onResult:null};o=P(n.ignore,f,u)}const c=a((f,h=!1)=>{const{isMatch:l,match:p,output:C}=P.test(f,i,e,{glob:t,posix:s}),g={glob:t,state:D,regex:i,posix:s,input:f,output:C,match:p,isMatch:l};return typeof n.onResult=="function"&&n.onResult(g),l===!1?(g.isMatch=!1,h?g:!1):o(f)?(typeof n.onIgnore=="function"&&n.onIgnore(g),g.isMatch=!1,h?g:!1):(typeof n.onMatch=="function"&&n.onMatch(g),h?g:!0)},"matcher");return u&&(c.state=D),c},"picomatch$3");P.test=(t,e,u,{glob:r,posix:n}={})=>{if(typeof t!="string")throw new TypeError("Expected input to be a string");if(t==="")return{isMatch:!1,output:""};const s=u||{},i=s.format||(n?Gt.toPosixSlashes:null);let D=t===r,o=D&&i?i(t):t;return D===!1&&(o=i?i(t):t,D=o===r),(D===!1||s.capture===!0)&&(s.matchBase===!0||s.basename===!0?D=P.matchBase(t,e,u,n):D=e.exec(o)),{isMatch:!!D,match:D,output:o}},P.matchBase=(t,e,u,r=Gt.isWindows(u))=>(e instanceof RegExp?e:P.makeRe(e,u)).test(oD.basename(t)),P.isMatch=(t,e,u)=>P(e,u)(t),P.parse=(t,e)=>Array.isArray(t)?t.map(u=>P.parse(u,e)):Mt(t,{...e,fastpaths:!1}),P.scan=(t,e)=>aD(t,e),P.compileRe=(t,e,u=!1,r=!1)=>{if(u===!0)return t.output;const n=e||{},s=n.contains?"":"^",i=n.contains?"":"$";let D=`${s}(?:${t.output})${i}`;t&&t.negated===!0&&(D=`^(?!${D}).*$`);const o=P.toRegex(D,e);return r===!0&&(o.state=t),o},P.makeRe=(t,e={},u=!1,r=!1)=>{if(!t||typeof t!="string")throw new TypeError("Expected a non-empty string");let n={negated:!1,fastpaths:!0};return e.fastpaths!==!1&&(t[0]==="."||t[0]==="*")&&(n.output=Mt.fastpaths(t,e)),n.output||(n=Mt(t,e)),P.compileRe(n,e,u,r)},P.toRegex=(t,e)=>{try{const u=e||{};return new RegExp(t,u.flags||(u.nocase?"i":""))}catch(u){if(e&&e.debug===!0)throw u;return/$^/}},P.constants=lD;var fD=P,Fr=fD;const Re=oe,{Readable:hD}=On,be=z,{promisify:Xe}=_e,Wt=Fr,dD=Xe(Re.readdir),ED=Xe(Re.stat),gr=Xe(Re.lstat),pD=Xe(Re.realpath),CD="!",mr="READDIRP_RECURSIVE_ERROR",FD=new Set(["ENOENT","EPERM","EACCES","ELOOP",mr]),jt="files",_r="directories",Qe="files_directories",Ze="all",Ar=[jt,_r,Qe,Ze],gD=a(t=>FD.has(t.code),"isNormalFlowError"),[yr,mD]=process.versions.node.split(".").slice(0,2).map(t=>Number.parseInt(t,10)),_D=process.platform==="win32"&&(yr>10||yr===10&&mD>=5),wr=a(t=>{if(t!==void 0){if(typeof t=="function")return t;if(typeof t=="string"){const e=Wt(t.trim());return u=>e(u.basename)}if(Array.isArray(t)){const e=[],u=[];for(const r of t){const n=r.trim();n.charAt(0)===CD?u.push(Wt(n.slice(1))):e.push(Wt(n))}return u.length>0?e.length>0?r=>e.some(n=>n(r.basename))&&!u.some(n=>n(r.basename)):r=>!u.some(n=>n(r.basename)):r=>e.some(n=>n(r.basename))}}},"normalizeFilter");class at extends hD{static{a(this,"ReaddirpStream")}static get defaultOptions(){return{root:".",fileFilter:a(e=>!0,"fileFilter"),directoryFilter:a(e=>!0,"directoryFilter"),type:jt,lstat:!1,depth:2147483648,alwaysStat:!1}}constructor(e={}){super({objectMode:!0,autoDestroy:!0,highWaterMark:e.highWaterMark||4096});const u={...at.defaultOptions,...e},{root:r,type:n}=u;this._fileFilter=wr(u.fileFilter),this._directoryFilter=wr(u.directoryFilter);const s=u.lstat?gr:ED;_D?this._stat=i=>s(i,{bigint:!0}):this._stat=s,this._maxDepth=u.depth,this._wantsDir=[_r,Qe,Ze].includes(n),this._wantsFile=[jt,Qe,Ze].includes(n),this._wantsEverything=n===Ze,this._root=be.resolve(r),this._isDirent="Dirent"in Re&&!u.alwaysStat,this._statsProp=this._isDirent?"dirent":"stats",this._rdOptions={encoding:"utf8",withFileTypes:this._isDirent},this.parents=[this._exploreDir(r,1)],this.reading=!1,this.parent=void 0}async _read(e){if(!this.reading){this.reading=!0;try{for(;!this.destroyed&&e>0;){const{path:u,depth:r,files:n=[]}=this.parent||{};if(n.length>0){const s=n.splice(0,e).map(i=>this._formatEntry(i,u));for(const i of await Promise.all(s)){if(this.destroyed)return;const D=await this._getEntryType(i);D==="directory"&&this._directoryFilter(i)?(r<=this._maxDepth&&this.parents.push(this._exploreDir(i.fullPath,r+1)),this._wantsDir&&(this.push(i),e--)):(D==="file"||this._includeAsFile(i))&&this._fileFilter(i)&&this._wantsFile&&(this.push(i),e--)}}else{const s=this.parents.pop();if(!s){this.push(null);break}if(this.parent=await s,this.destroyed)return}}}catch(u){this.destroy(u)}finally{this.reading=!1}}}async _exploreDir(e,u){let r;try{r=await dD(e,this._rdOptions)}catch(n){this._onError(n)}return{files:r,depth:u,path:e}}async _formatEntry(e,u){let r;try{const n=this._isDirent?e.name:e,s=be.resolve(be.join(u,n));r={path:be.relative(this._root,s),fullPath:s,basename:n},r[this._statsProp]=this._isDirent?e:await this._stat(s)}catch(n){this._onError(n)}return r}_onError(e){gD(e)&&!this.destroyed?this.emit("warn",e):this.destroy(e)}async _getEntryType(e){const u=e&&e[this._statsProp];if(u){if(u.isFile())return"file";if(u.isDirectory())return"directory";if(u&&u.isSymbolicLink()){const r=e.fullPath;try{const n=await pD(r),s=await gr(n);if(s.isFile())return"file";if(s.isDirectory()){const i=n.length;if(r.startsWith(n)&&r.substr(i,1)===be.sep){const D=new Error(`Circular symlink detected: "${r}" points to "${n}"`);return D.code=mr,this._onError(D)}return"directory"}}catch(n){this._onError(n)}}}}_includeAsFile(e){const u=e&&e[this._statsProp];return u&&this._wantsEverything&&!u.isDirectory()}}const Ce=a((t,e={})=>{let u=e.entryType||e.type;if(u==="both"&&(u=Qe),u&&(e.type=u),t){if(typeof t!="string")throw new TypeError("readdirp: root argument must be a string. Usage: readdirp(root, options)");if(u&&!Ar.includes(u))throw new Error(`readdirp: Invalid type passed. Use one of ${Ar.join(", ")}`)}else throw new Error("readdirp: root argument is required. Usage: readdirp(root, options)");return e.root=t,new at(e)},"readdirp$1"),AD=a((t,e={})=>new Promise((u,r)=>{const n=[];Ce(t,e).on("data",s=>n.push(s)).on("end",()=>u(n)).on("error",s=>r(s))}),"readdirpPromise");Ce.promise=AD,Ce.ReaddirpStream=at,Ce.default=Ce;var yD=Ce,Ut={exports:{}};/*!
 * normalize-path <https://github.com/jonschlinkert/normalize-path>
 *
 * Copyright (c) 2014-2018, Jon Schlinkert.
 * Released under the MIT License.
 */var Rr=a(function(t,e){if(typeof t!="string")throw new TypeError("expected path to be a string");if(t==="\\"||t==="/")return"/";var u=t.length;if(u<=1)return t;var r="";if(u>4&&t[3]==="\\"){var n=t[2];(n==="?"||n===".")&&t.slice(0,2)==="\\\\"&&(t=t.slice(2),r="//")}var s=t.split(/[/\\]+/);return e!==!1&&s[s.length-1]===""&&s.pop(),r+s.join("/")},"normalizePath$2"),wD=Ut.exports;Object.defineProperty(wD,"__esModule",{value:!0});const br=Fr,RD=Rr,vr="!",bD={returnIndex:!1},vD=a(t=>Array.isArray(t)?t:[t],"arrify$1"),SD=a((t,e)=>{if(typeof t=="function")return t;if(typeof t=="string"){const u=br(t,e);return r=>t===r||u(r)}return t instanceof RegExp?u=>t.test(u):u=>!1},"createPattern"),Sr=a((t,e,u,r)=>{const n=Array.isArray(u),s=n?u[0]:u;if(!n&&typeof s!="string")throw new TypeError("anymatch: second argument must be a string: got "+Object.prototype.toString.call(s));const i=RD(s,!1);for(let o=0;o<e.length;o++){const c=e[o];if(c(i))return r?-1:!1}const D=n&&[i].concat(u.slice(1));for(let o=0;o<t.length;o++){const c=t[o];if(n?c(...D):c(i))return r?o:!0}return r?-1:!1},"matchPatterns"),Kt=a((t,e,u=bD)=>{if(t==null)throw new TypeError("anymatch: specify first argument");const r=typeof u=="boolean"?{returnIndex:u}:u,n=r.returnIndex||!1,s=vD(t),i=s.filter(o=>typeof o=="string"&&o.charAt(0)===vr).map(o=>o.slice(1)).map(o=>br(o,r)),D=s.filter(o=>typeof o!="string"||typeof o=="string"&&o.charAt(0)!==vr).map(o=>SD(o,r));return e==null?(o,c=!1)=>Sr(D,i,o,typeof c=="boolean"?c:!1):Sr(D,i,e,n)},"anymatch$1");Kt.default=Kt,Ut.exports=Kt;var BD=Ut.exports;/*!
 * is-extglob <https://github.com/jonschlinkert/is-extglob>
 *
 * Copyright (c) 2014-2016, Jon Schlinkert.
 * Licensed under the MIT License.
 */var $D=a(function(e){if(typeof e!="string"||e==="")return!1;for(var u;u=/(\\).|([@?!+*]\(.*\))/g.exec(e);){if(u[2])return!0;e=e.slice(u.index+u[0].length)}return!1},"isExtglob");/*!
 * is-glob <https://github.com/jonschlinkert/is-glob>
 *
 * Copyright (c) 2014-2017, Jon Schlinkert.
 * Released under the MIT License.
 */var TD=$D,Br={"{":"}","(":")","[":"]"},xD=a(function(t){if(t[0]==="!")return!0;for(var e=0,u=-2,r=-2,n=-2,s=-2,i=-2;e<t.length;){if(t[e]==="*"||t[e+1]==="?"&&/[\].+)]/.test(t[e])||r!==-1&&t[e]==="["&&t[e+1]!=="]"&&(r<e&&(r=t.indexOf("]",e)),r>e&&(i===-1||i>r||(i=t.indexOf("\\",e),i===-1||i>r)))||n!==-1&&t[e]==="{"&&t[e+1]!=="}"&&(n=t.indexOf("}",e),n>e&&(i=t.indexOf("\\",e),i===-1||i>n))||s!==-1&&t[e]==="("&&t[e+1]==="?"&&/[:!=]/.test(t[e+2])&&t[e+3]!==")"&&(s=t.indexOf(")",e),s>e&&(i=t.indexOf("\\",e),i===-1||i>s))||u!==-1&&t[e]==="("&&t[e+1]!=="|"&&(u<e&&(u=t.indexOf("|",e)),u!==-1&&t[u+1]!==")"&&(s=t.indexOf(")",u),s>u&&(i=t.indexOf("\\",u),i===-1||i>s))))return!0;if(t[e]==="\\"){var D=t[e+1];e+=2;var o=Br[D];if(o){var c=t.indexOf(o,e);c!==-1&&(e=c+1)}if(t[e]==="!")return!0}else e++}return!1},"strictCheck"),OD=a(function(t){if(t[0]==="!")return!0;for(var e=0;e<t.length;){if(/[*?{}()[\]]/.test(t[e]))return!0;if(t[e]==="\\"){var u=t[e+1];e+=2;var r=Br[u];if(r){var n=t.indexOf(r,e);n!==-1&&(e=n+1)}if(t[e]==="!")return!0}else e++}return!1},"relaxedCheck"),$r=a(function(e,u){if(typeof e!="string"||e==="")return!1;if(TD(e))return!0;var r=xD;return u&&u.strict===!1&&(r=OD),r(e)},"isGlob"),ND=$r,HD=z.posix.dirname,PD=Au.platform()==="win32",Vt="/",LD=/\\/g,ID=/[\{\[].*[\}\]]$/,kD=/(^|[^\\])([\{\[]|\([^\)]+$)/,MD=/\\([\!\*\?\|\[\]\(\)\{\}])/g,GD=a(function(e,u){var r=Object.assign({flipBackslashes:!0},u);r.flipBackslashes&&PD&&e.indexOf(Vt)<0&&(e=e.replace(LD,Vt)),ID.test(e)&&(e+=Vt),e+="a";do e=HD(e);while(ND(e)||kD.test(e));return e.replace(MD,"$1")},"globParent"),Je={};(function(t){t.isInteger=e=>typeof e=="number"?Number.isInteger(e):typeof e=="string"&&e.trim()!==""?Number.isInteger(Number(e)):!1,t.find=(e,u)=>e.nodes.find(r=>r.type===u),t.exceedsLimit=(e,u,r=1,n)=>n===!1||!t.isInteger(e)||!t.isInteger(u)?!1:(Number(u)-Number(e))/Number(r)>=n,t.escapeNode=(e,u=0,r)=>{let n=e.nodes[u];n&&(r&&n.type===r||n.type==="open"||n.type==="close")&&n.escaped!==!0&&(n.value="\\"+n.value,n.escaped=!0)},t.encloseBrace=e=>e.type!=="brace"||e.commas>>0+e.ranges>>0?!1:(e.invalid=!0,!0),t.isInvalidBrace=e=>e.type!=="brace"?!1:e.invalid===!0||e.dollar?!0:!(e.commas>>0+e.ranges>>0)||e.open!==!0||e.close!==!0?(e.invalid=!0,!0):!1,t.isOpenOrClose=e=>e.type==="open"||e.type==="close"?!0:e.open===!0||e.close===!0,t.reduce=e=>e.reduce((u,r)=>(r.type==="text"&&u.push(r.value),r.type==="range"&&(r.type="text"),u),[]),t.flatten=(...e)=>{const u=[],r=a(n=>{for(let s=0;s<n.length;s++){let i=n[s];Array.isArray(i)?r(i):i!==void 0&&u.push(i)}return u},"flat");return r(e),u}})(Je);const Tr=Je;var zt=a((t,e={})=>{let u=a((r,n={})=>{let s=e.escapeInvalid&&Tr.isInvalidBrace(n),i=r.invalid===!0&&e.escapeInvalid===!0,D="";if(r.value)return(s||i)&&Tr.isOpenOrClose(r)?"\\"+r.value:r.value;if(r.value)return r.value;if(r.nodes)for(let o of r.nodes)D+=u(o);return D},"stringify");return u(t)},"stringify$4");/*!
 * is-number <https://github.com/jonschlinkert/is-number>
 *
 * Copyright (c) 2014-present, Jon Schlinkert.
 * Released under the MIT License.
 */var WD=a(function(t){return typeof t=="number"?t-t===0:typeof t=="string"&&t.trim()!==""?Number.isFinite?Number.isFinite(+t):isFinite(+t):!1},"isNumber$2");/*!
 * to-regex-range <https://github.com/micromatch/to-regex-range>
 *
 * Copyright (c) 2015-present, Jon Schlinkert.
 * Released under the MIT License.
 */const xr=WD,le=a((t,e,u)=>{if(xr(t)===!1)throw new TypeError("toRegexRange: expected the first argument to be a number");if(e===void 0||t===e)return String(t);if(xr(e)===!1)throw new TypeError("toRegexRange: expected the second argument to be a number.");let r={relaxZeros:!0,...u};typeof r.strictZeros=="boolean"&&(r.relaxZeros=r.strictZeros===!1);let n=String(r.relaxZeros),s=String(r.shorthand),i=String(r.capture),D=String(r.wrap),o=t+":"+e+"="+n+s+i+D;if(le.cache.hasOwnProperty(o))return le.cache[o].result;let c=Math.min(t,e),f=Math.max(t,e);if(Math.abs(c-f)===1){let g=t+"|"+e;return r.capture?`(${g})`:r.wrap===!1?g:`(?:${g})`}let h=Ir(t)||Ir(e),l={min:t,max:e,a:c,b:f},p=[],C=[];if(h&&(l.isPadded=h,l.maxLen=String(l.max).length),c<0){let g=f<0?Math.abs(f):1;C=Or(g,Math.abs(c),l,r),c=l.a=0}return f>=0&&(p=Or(c,f,l,r)),l.negatives=C,l.positives=p,l.result=jD(C,p),r.capture===!0?l.result=`(${l.result})`:r.wrap!==!1&&p.length+C.length>1&&(l.result=`(?:${l.result})`),le.cache[o]=l,l.result},"toRegexRange$1");function jD(t,e,u){let r=Yt(t,e,"-",!1)||[],n=Yt(e,t,"",!1)||[],s=Yt(t,e,"-?",!0)||[];return r.concat(s).concat(n).join("|")}a(jD,"collatePatterns");function UD(t,e){let u=1,r=1,n=Hr(t,u),s=new Set([e]);for(;t<=n&&n<=e;)s.add(n),u+=1,n=Hr(t,u);for(n=Pr(e+1,r)-1;t<n&&n<=e;)s.add(n),r+=1,n=Pr(e+1,r)-1;return s=[...s],s.sort(zD),s}a(UD,"splitToRanges");function KD(t,e,u){if(t===e)return{pattern:t,count:[],digits:0};let r=VD(t,e),n=r.length,s="",i=0;for(let D=0;D<n;D++){let[o,c]=r[D];o===c?s+=o:o!=="0"||c!=="9"?s+=YD(o,c):i++}return i&&(s+=u.shorthand===!0?"\\d":"[0-9]"),{pattern:s,count:[i],digits:n}}a(KD,"rangeToPattern");function Or(t,e,u,r){let n=UD(t,e),s=[],i=t,D;for(let o=0;o<n.length;o++){let c=n[o],f=KD(String(i),String(c),r),h="";if(!u.isPadded&&D&&D.pattern===f.pattern){D.count.length>1&&D.count.pop(),D.count.push(f.count[0]),D.string=D.pattern+Lr(D.count),i=c+1;continue}u.isPadded&&(h=qD(c,u,r)),f.string=h+f.pattern+Lr(f.count),s.push(f),i=c+1,D=f}return s}a(Or,"splitToPatterns");function Yt(t,e,u,r,n){let s=[];for(let i of t){let{string:D}=i;!r&&!Nr(e,"string",D)&&s.push(u+D),r&&Nr(e,"string",D)&&s.push(u+D)}return s}a(Yt,"filterPatterns");function VD(t,e){let u=[];for(let r=0;r<t.length;r++)u.push([t[r],e[r]]);return u}a(VD,"zip");function zD(t,e){return t>e?1:e>t?-1:0}a(zD,"compare");function Nr(t,e,u){return t.some(r=>r[e]===u)}a(Nr,"contains");function Hr(t,e){return Number(String(t).slice(0,-e)+"9".repeat(e))}a(Hr,"countNines");function Pr(t,e){return t-t%Math.pow(10,e)}a(Pr,"countZeros");function Lr(t){let[e=0,u=""]=t;return u||e>1?`{${e+(u?","+u:"")}}`:""}a(Lr,"toQuantifier");function YD(t,e,u){return`[${t}${e-t===1?"":"-"}${e}]`}a(YD,"toCharacterClass");function Ir(t){return/^-?(0+)\d/.test(t)}a(Ir,"hasPadding");function qD(t,e,u){if(!e.isPadded)return t;let r=Math.abs(e.maxLen-String(t).length),n=u.relaxZeros!==!1;switch(r){case 0:return"";case 1:return n?"0?":"0";case 2:return n?"0{0,2}":"00";default:return n?`0{0,${r}}`:`0{${r}}`}}a(qD,"padZeros"),le.cache={},le.clearCache=()=>le.cache={};var XD=le;/*!
 * fill-range <https://github.com/jonschlinkert/fill-range>
 *
 * Copyright (c) 2014-present, Jon Schlinkert.
 * Licensed under the MIT License.
 */const QD=_e,kr=XD,Mr=a(t=>t!==null&&typeof t=="object"&&!Array.isArray(t),"isObject"),ZD=a(t=>e=>t===!0?Number(e):String(e),"transform"),qt=a(t=>typeof t=="number"||typeof t=="string"&&t!=="","isValidValue"),ve=a(t=>Number.isInteger(+t),"isNumber"),Xt=a(t=>{let e=`${t}`,u=-1;if(e[0]==="-"&&(e=e.slice(1)),e==="0")return!1;for(;e[++u]==="0";);return u>0},"zeros"),JD=a((t,e,u)=>typeof t=="string"||typeof e=="string"?!0:u.stringify===!0,"stringify$3"),eo=a((t,e,u)=>{if(e>0){let r=t[0]==="-"?"-":"";r&&(t=t.slice(1)),t=r+t.padStart(r?e-1:e,"0")}return u===!1?String(t):t},"pad"),Gr=a((t,e)=>{let u=t[0]==="-"?"-":"";for(u&&(t=t.slice(1),e--);t.length<e;)t="0"+t;return u?"-"+t:t},"toMaxLen"),to=a((t,e)=>{t.negatives.sort((i,D)=>i<D?-1:i>D?1:0),t.positives.sort((i,D)=>i<D?-1:i>D?1:0);let u=e.capture?"":"?:",r="",n="",s;return t.positives.length&&(r=t.positives.join("|")),t.negatives.length&&(n=`-(${u}${t.negatives.join("|")})`),r&&n?s=`${r}|${n}`:s=r||n,e.wrap?`(${u}${s})`:s},"toSequence"),Wr=a((t,e,u,r)=>{if(u)return kr(t,e,{wrap:!1,...r});let n=String.fromCharCode(t);if(t===e)return n;let s=String.fromCharCode(e);return`[${n}-${s}]`},"toRange"),jr=a((t,e,u)=>{if(Array.isArray(t)){let r=u.wrap===!0,n=u.capture?"":"?:";return r?`(${n}${t.join("|")})`:t.join("|")}return kr(t,e,u)},"toRegex"),Ur=a((...t)=>new RangeError("Invalid range arguments: "+QD.inspect(...t)),"rangeError"),Kr=a((t,e,u)=>{if(u.strictRanges===!0)throw Ur([t,e]);return[]},"invalidRange"),uo=a((t,e)=>{if(e.strictRanges===!0)throw new TypeError(`Expected step "${t}" to be a number`);return[]},"invalidStep"),ro=a((t,e,u=1,r={})=>{let n=Number(t),s=Number(e);if(!Number.isInteger(n)||!Number.isInteger(s)){if(r.strictRanges===!0)throw Ur([t,e]);return[]}n===0&&(n=0),s===0&&(s=0);let i=n>s,D=String(t),o=String(e),c=String(u);u=Math.max(Math.abs(u),1);let f=Xt(D)||Xt(o)||Xt(c),h=f?Math.max(D.length,o.length,c.length):0,l=f===!1&&JD(t,e,r)===!1,p=r.transform||ZD(l);if(r.toRegex&&u===1)return Wr(Gr(t,h),Gr(e,h),!0,r);let C={negatives:[],positives:[]},g=a(H=>C[H<0?"negatives":"positives"].push(Math.abs(H)),"push"),y=[],B=0;for(;i?n>=s:n<=s;)r.toRegex===!0&&u>1?g(n):y.push(eo(p(n,B),h,l)),n=i?n-u:n+u,B++;return r.toRegex===!0?u>1?to(C,r):jr(y,null,{wrap:!1,...r}):y},"fillNumbers"),no=a((t,e,u=1,r={})=>{if(!ve(t)&&t.length>1||!ve(e)&&e.length>1)return Kr(t,e,r);let n=r.transform||(l=>String.fromCharCode(l)),s=`${t}`.charCodeAt(0),i=`${e}`.charCodeAt(0),D=s>i,o=Math.min(s,i),c=Math.max(s,i);if(r.toRegex&&u===1)return Wr(o,c,!1,r);let f=[],h=0;for(;D?s>=i:s<=i;)f.push(n(s,h)),s=D?s-u:s+u,h++;return r.toRegex===!0?jr(f,null,{wrap:!1,options:r}):f},"fillLetters"),et=a((t,e,u,r={})=>{if(e==null&&qt(t))return[t];if(!qt(t)||!qt(e))return Kr(t,e,r);if(typeof u=="function")return et(t,e,1,{transform:u});if(Mr(u))return et(t,e,0,u);let n={...r};return n.capture===!0&&(n.wrap=!0),u=u||n.step||1,ve(u)?ve(t)&&ve(e)?ro(t,e,u,n):no(t,e,Math.max(Math.abs(u),1),n):u!=null&&!Mr(u)?uo(u,n):et(t,e,1,u)},"fill$2");var Vr=et;const so=Vr,zr=Je,io=a((t,e={})=>{let u=a((r,n={})=>{let s=zr.isInvalidBrace(n),i=r.invalid===!0&&e.escapeInvalid===!0,D=s===!0||i===!0,o=e.escapeInvalid===!0?"\\":"",c="";if(r.isOpen===!0||r.isClose===!0)return o+r.value;if(r.type==="open")return D?o+r.value:"(";if(r.type==="close")return D?o+r.value:")";if(r.type==="comma")return r.prev.type==="comma"?"":D?r.value:"|";if(r.value)return r.value;if(r.nodes&&r.ranges>0){let f=zr.reduce(r.nodes),h=so(...f,{...e,wrap:!1,toRegex:!0});if(h.length!==0)return f.length>1&&h.length>1?`(${h})`:h}if(r.nodes)for(let f of r.nodes)c+=u(f,r);return c},"walk");return u(t)},"compile$1");var Do=io;const oo=Vr,Yr=zt,Fe=Je,ce=a((t="",e="",u=!1)=>{let r=[];if(t=[].concat(t),e=[].concat(e),!e.length)return t;if(!t.length)return u?Fe.flatten(e).map(n=>`{${n}}`):e;for(let n of t)if(Array.isArray(n))for(let s of n)r.push(ce(s,e,u));else for(let s of e)u===!0&&typeof s=="string"&&(s=`{${s}}`),r.push(Array.isArray(s)?ce(n,s,u):n+s);return Fe.flatten(r)},"append"),ao=a((t,e={})=>{let u=e.rangeLimit===void 0?1e3:e.rangeLimit,r=a((n,s={})=>{n.queue=[];let i=s,D=s.queue;for(;i.type!=="brace"&&i.type!=="root"&&i.parent;)i=i.parent,D=i.queue;if(n.invalid||n.dollar){D.push(ce(D.pop(),Yr(n,e)));return}if(n.type==="brace"&&n.invalid!==!0&&n.nodes.length===2){D.push(ce(D.pop(),["{}"]));return}if(n.nodes&&n.ranges>0){let h=Fe.reduce(n.nodes);if(Fe.exceedsLimit(...h,e.step,u))throw new RangeError("expanded array length exceeds range limit. Use options.rangeLimit to increase or disable the limit.");let l=oo(...h,e);l.length===0&&(l=Yr(n,e)),D.push(ce(D.pop(),l)),n.nodes=[];return}let o=Fe.encloseBrace(n),c=n.queue,f=n;for(;f.type!=="brace"&&f.type!=="root"&&f.parent;)f=f.parent,c=f.queue;for(let h=0;h<n.nodes.length;h++){let l=n.nodes[h];if(l.type==="comma"&&n.type==="brace"){h===1&&c.push(""),c.push("");continue}if(l.type==="close"){D.push(ce(D.pop(),c,o));continue}if(l.value&&l.type!=="open"){c.push(ce(c.pop(),l.value));continue}l.nodes&&r(l,n)}return c},"walk");return Fe.flatten(r(t))},"expand$1");var lo=ao,co={MAX_LENGTH:1024*64,CHAR_0:"0",CHAR_9:"9",CHAR_UPPERCASE_A:"A",CHAR_LOWERCASE_A:"a",CHAR_UPPERCASE_Z:"Z",CHAR_LOWERCASE_Z:"z",CHAR_LEFT_PARENTHESES:"(",CHAR_RIGHT_PARENTHESES:")",CHAR_ASTERISK:"*",CHAR_AMPERSAND:"&",CHAR_AT:"@",CHAR_BACKSLASH:"\\",CHAR_BACKTICK:"`",CHAR_CARRIAGE_RETURN:"\r",CHAR_CIRCUMFLEX_ACCENT:"^",CHAR_COLON:":",CHAR_COMMA:",",CHAR_DOLLAR:"$",CHAR_DOT:".",CHAR_DOUBLE_QUOTE:'"',CHAR_EQUAL:"=",CHAR_EXCLAMATION_MARK:"!",CHAR_FORM_FEED:"\f",CHAR_FORWARD_SLASH:"/",CHAR_HASH:"#",CHAR_HYPHEN_MINUS:"-",CHAR_LEFT_ANGLE_BRACKET:"<",CHAR_LEFT_CURLY_BRACE:"{",CHAR_LEFT_SQUARE_BRACKET:"[",CHAR_LINE_FEED:`
`,CHAR_NO_BREAK_SPACE:"\xA0",CHAR_PERCENT:"%",CHAR_PLUS:"+",CHAR_QUESTION_MARK:"?",CHAR_RIGHT_ANGLE_BRACKET:">",CHAR_RIGHT_CURLY_BRACE:"}",CHAR_RIGHT_SQUARE_BRACKET:"]",CHAR_SEMICOLON:";",CHAR_SINGLE_QUOTE:"'",CHAR_SPACE:" ",CHAR_TAB:"	",CHAR_UNDERSCORE:"_",CHAR_VERTICAL_LINE:"|",CHAR_ZERO_WIDTH_NOBREAK_SPACE:"\uFEFF"};const fo=zt,{MAX_LENGTH:qr,CHAR_BACKSLASH:Qt,CHAR_BACKTICK:ho,CHAR_COMMA:Eo,CHAR_DOT:po,CHAR_LEFT_PARENTHESES:Co,CHAR_RIGHT_PARENTHESES:Fo,CHAR_LEFT_CURLY_BRACE:go,CHAR_RIGHT_CURLY_BRACE:mo,CHAR_LEFT_SQUARE_BRACKET:Xr,CHAR_RIGHT_SQUARE_BRACKET:Qr,CHAR_DOUBLE_QUOTE:_o,CHAR_SINGLE_QUOTE:Ao,CHAR_NO_BREAK_SPACE:yo,CHAR_ZERO_WIDTH_NOBREAK_SPACE:wo}=co,Ro=a((t,e={})=>{if(typeof t!="string")throw new TypeError("Expected a string");let u=e||{},r=typeof u.maxLength=="number"?Math.min(qr,u.maxLength):qr;if(t.length>r)throw new SyntaxError(`Input length (${t.length}), exceeds max characters (${r})`);let n={type:"root",input:t,nodes:[]},s=[n],i=n,D=n,o=0,c=t.length,f=0,h=0,l;const p=a(()=>t[f++],"advance"),C=a(g=>{if(g.type==="text"&&D.type==="dot"&&(D.type="text"),D&&D.type==="text"&&g.type==="text"){D.value+=g.value;return}return i.nodes.push(g),g.parent=i,g.prev=D,D=g,g},"push");for(C({type:"bos"});f<c;)if(i=s[s.length-1],l=p(),!(l===wo||l===yo)){if(l===Qt){C({type:"text",value:(e.keepEscaping?l:"")+p()});continue}if(l===Qr){C({type:"text",value:"\\"+l});continue}if(l===Xr){o++;let g;for(;f<c&&(g=p());){if(l+=g,g===Xr){o++;continue}if(g===Qt){l+=p();continue}if(g===Qr&&(o--,o===0))break}C({type:"text",value:l});continue}if(l===Co){i=C({type:"paren",nodes:[]}),s.push(i),C({type:"text",value:l});continue}if(l===Fo){if(i.type!=="paren"){C({type:"text",value:l});continue}i=s.pop(),C({type:"text",value:l}),i=s[s.length-1];continue}if(l===_o||l===Ao||l===ho){let g=l,y;for(e.keepQuotes!==!0&&(l="");f<c&&(y=p());){if(y===Qt){l+=y+p();continue}if(y===g){e.keepQuotes===!0&&(l+=y);break}l+=y}C({type:"text",value:l});continue}if(l===go){h++;let y={type:"brace",open:!0,close:!1,dollar:D.value&&D.value.slice(-1)==="$"||i.dollar===!0,depth:h,commas:0,ranges:0,nodes:[]};i=C(y),s.push(i),C({type:"open",value:l});continue}if(l===mo){if(i.type!=="brace"){C({type:"text",value:l});continue}let g="close";i=s.pop(),i.close=!0,C({type:g,value:l}),h--,i=s[s.length-1];continue}if(l===Eo&&h>0){if(i.ranges>0){i.ranges=0;let g=i.nodes.shift();i.nodes=[g,{type:"text",value:fo(i)}]}C({type:"comma",value:l}),i.commas++;continue}if(l===po&&h>0&&i.commas===0){let g=i.nodes;if(h===0||g.length===0){C({type:"text",value:l});continue}if(D.type==="dot"){if(i.range=[],D.value+=l,D.type="range",i.nodes.length!==3&&i.nodes.length!==5){i.invalid=!0,i.ranges=0,D.type="text";continue}i.ranges++,i.args=[];continue}if(D.type==="range"){g.pop();let y=g[g.length-1];y.value+=D.value+l,D=y,i.ranges--;continue}C({type:"dot",value:l});continue}C({type:"text",value:l})}do if(i=s.pop(),i.type!=="root"){i.nodes.forEach(B=>{B.nodes||(B.type==="open"&&(B.isOpen=!0),B.type==="close"&&(B.isClose=!0),B.nodes||(B.type="text"),B.invalid=!0)});let g=s[s.length-1],y=g.nodes.indexOf(i);g.nodes.splice(y,1,...i.nodes)}while(s.length>0);return C({type:"eos"}),n},"parse$1");var bo=Ro;const Zr=zt,vo=Do,So=lo,Bo=bo,q=a((t,e={})=>{let u=[];if(Array.isArray(t))for(let r of t){let n=q.create(r,e);Array.isArray(n)?u.push(...n):u.push(n)}else u=[].concat(q.create(t,e));return e&&e.expand===!0&&e.nodupes===!0&&(u=[...new Set(u)]),u},"braces$1");q.parse=(t,e={})=>Bo(t,e),q.stringify=(t,e={})=>Zr(typeof t=="string"?q.parse(t,e):t,e),q.compile=(t,e={})=>(typeof t=="string"&&(t=q.parse(t,e)),vo(t,e)),q.expand=(t,e={})=>{typeof t=="string"&&(t=q.parse(t,e));let u=So(t,e);return e.noempty===!0&&(u=u.filter(Boolean)),e.nodupes===!0&&(u=[...new Set(u)]),u},q.create=(t,e={})=>t===""||t.length<3?[t]:e.expand!==!0?q.compile(t,e):q.expand(t,e);var $o=q,To=["3dm","3ds","3g2","3gp","7z","a","aac","adp","afdesign","afphoto","afpub","ai","aif","aiff","alz","ape","apk","appimage","ar","arj","asf","au","avi","bak","baml","bh","bin","bk","bmp","btif","bz2","bzip2","cab","caf","cgm","class","cmx","cpio","cr2","cur","dat","dcm","deb","dex","djvu","dll","dmg","dng","doc","docm","docx","dot","dotm","dra","DS_Store","dsk","dts","dtshd","dvb","dwg","dxf","ecelp4800","ecelp7470","ecelp9600","egg","eol","eot","epub","exe","f4v","fbs","fh","fla","flac","flatpak","fli","flv","fpx","fst","fvt","g3","gh","gif","graffle","gz","gzip","h261","h263","h264","icns","ico","ief","img","ipa","iso","jar","jpeg","jpg","jpgv","jpm","jxr","key","ktx","lha","lib","lvp","lz","lzh","lzma","lzo","m3u","m4a","m4v","mar","mdi","mht","mid","midi","mj2","mka","mkv","mmr","mng","mobi","mov","movie","mp3","mp4","mp4a","mpeg","mpg","mpga","mxu","nef","npx","numbers","nupkg","o","odp","ods","odt","oga","ogg","ogv","otf","ott","pages","pbm","pcx","pdb","pdf","pea","pgm","pic","png","pnm","pot","potm","potx","ppa","ppam","ppm","pps","ppsm","ppsx","ppt","pptm","pptx","psd","pya","pyc","pyo","pyv","qt","rar","ras","raw","resources","rgb","rip","rlc","rmf","rmvb","rpm","rtf","rz","s3m","s7z","scpt","sgi","shar","snap","sil","sketch","slk","smv","snk","so","stl","suo","sub","swf","tar","tbz","tbz2","tga","tgz","thmx","tif","tiff","tlz","ttc","ttf","txz","udf","uvh","uvi","uvm","uvp","uvs","uvu","viv","vob","war","wav","wax","wbmp","wdp","weba","webm","webp","whl","wim","wm","wma","wmv","wmx","woff","woff2","wrm","wvx","xbm","xif","xla","xlam","xls","xlsb","xlsm","xlsx","xlt","xltm","xltx","xm","xmind","xpi","xpm","xwd","xz","z","zip","zipx"],xo=To;const Oo=z,No=xo,Ho=new Set(No);var Po=a(t=>Ho.has(Oo.extname(t).slice(1).toLowerCase()),"isBinaryPath$1"),tt={};(function(t){const{sep:e}=z,{platform:u}=process,r=Au;t.EV_ALL="all",t.EV_READY="ready",t.EV_ADD="add",t.EV_CHANGE="change",t.EV_ADD_DIR="addDir",t.EV_UNLINK="unlink",t.EV_UNLINK_DIR="unlinkDir",t.EV_RAW="raw",t.EV_ERROR="error",t.STR_DATA="data",t.STR_END="end",t.STR_CLOSE="close",t.FSEVENT_CREATED="created",t.FSEVENT_MODIFIED="modified",t.FSEVENT_DELETED="deleted",t.FSEVENT_MOVED="moved",t.FSEVENT_CLONED="cloned",t.FSEVENT_UNKNOWN="unknown",t.FSEVENT_FLAG_MUST_SCAN_SUBDIRS=1,t.FSEVENT_TYPE_FILE="file",t.FSEVENT_TYPE_DIRECTORY="directory",t.FSEVENT_TYPE_SYMLINK="symlink",t.KEY_LISTENERS="listeners",t.KEY_ERR="errHandlers",t.KEY_RAW="rawEmitters",t.HANDLER_KEYS=[t.KEY_LISTENERS,t.KEY_ERR,t.KEY_RAW],t.DOT_SLASH=`.${e}`,t.BACK_SLASH_RE=/\\/g,t.DOUBLE_SLASH_RE=/\/\//,t.SLASH_OR_BACK_SLASH_RE=/[/\\]/,t.DOT_RE=/\..*\.(sw[px])$|~$|\.subl.*\.tmp/,t.REPLACER_RE=/^\.[/\\]/,t.SLASH="/",t.SLASH_SLASH="//",t.BRACE_START="{",t.BANG="!",t.ONE_DOT=".",t.TWO_DOTS="..",t.STAR="*",t.GLOBSTAR="**",t.ROOT_GLOBSTAR="/**/*",t.SLASH_GLOBSTAR="/**",t.DIR_SUFFIX="Dir",t.ANYMATCH_OPTS={dot:!0},t.STRING_TYPE="string",t.FUNCTION_TYPE="function",t.EMPTY_STR="",t.EMPTY_FN=()=>{},t.IDENTITY_FN=n=>n,t.isWindows=u==="win32",t.isMacos=u==="darwin",t.isLinux=u==="linux",t.isIBMi=r.type()==="OS400"})(tt);const ne=oe,I=z,{promisify:Se}=_e,Lo=Po,{isWindows:Io,isLinux:ko,EMPTY_FN:Mo,EMPTY_STR:Go,KEY_LISTENERS:ge,KEY_ERR:Zt,KEY_RAW:Be,HANDLER_KEYS:Wo,EV_CHANGE:ut,EV_ADD:rt,EV_ADD_DIR:jo,EV_ERROR:Jr,STR_DATA:Uo,STR_END:Ko,BRACE_START:Vo,STAR:zo}=tt,Yo="watch",qo=Se(ne.open),en=Se(ne.stat),Xo=Se(ne.lstat),Qo=Se(ne.close),Jt=Se(ne.realpath),Zo={lstat:Xo,stat:en},eu=a((t,e)=>{t instanceof Set?t.forEach(e):e(t)},"foreach"),$e=a((t,e,u)=>{let r=t[e];r instanceof Set||(t[e]=r=new Set([r])),r.add(u)},"addAndConvert"),Jo=a(t=>e=>{const u=t[e];u instanceof Set?u.clear():delete t[e]},"clearItem"),Te=a((t,e,u)=>{const r=t[e];r instanceof Set?r.delete(u):r===u&&delete t[e]},"delFromSet"),tn=a(t=>t instanceof Set?t.size===0:!t,"isEmptySet"),nt=new Map;function un(t,e,u,r,n){const s=a((i,D)=>{u(t),n(i,D,{watchedPath:t}),D&&t!==D&&st(I.resolve(t,D),ge,I.join(t,D))},"handleEvent");try{return ne.watch(t,e,s)}catch(i){r(i)}}a(un,"createFsWatchInstance");const st=a((t,e,u,r,n)=>{const s=nt.get(t);s&&eu(s[e],i=>{i(u,r,n)})},"fsWatchBroadcast"),ea=a((t,e,u,r)=>{const{listener:n,errHandler:s,rawEmitter:i}=r;let D=nt.get(e),o;if(!u.persistent)return o=un(t,u,n,s,i),o.close.bind(o);if(D)$e(D,ge,n),$e(D,Zt,s),$e(D,Be,i);else{if(o=un(t,u,st.bind(null,e,ge),s,st.bind(null,e,Be)),!o)return;o.on(Jr,async c=>{const f=st.bind(null,e,Zt);if(D.watcherUnusable=!0,Io&&c.code==="EPERM")try{const h=await qo(t,"r");await Qo(h),f(c)}catch{}else f(c)}),D={listeners:n,errHandlers:s,rawEmitters:i,watcher:o},nt.set(e,D)}return()=>{Te(D,ge,n),Te(D,Zt,s),Te(D,Be,i),tn(D.listeners)&&(D.watcher.close(),nt.delete(e),Wo.forEach(Jo(D)),D.watcher=void 0,Object.freeze(D))}},"setFsWatchListener"),tu=new Map,ta=a((t,e,u,r)=>{const{listener:n,rawEmitter:s}=r;let i=tu.get(e);const D=i&&i.options;return D&&(D.persistent<u.persistent||D.interval>u.interval)&&(i.listeners,i.rawEmitters,ne.unwatchFile(e),i=void 0),i?($e(i,ge,n),$e(i,Be,s)):(i={listeners:n,rawEmitters:s,options:u,watcher:ne.watchFile(e,u,(o,c)=>{eu(i.rawEmitters,h=>{h(ut,e,{curr:o,prev:c})});const f=o.mtimeMs;(o.size!==c.size||f>c.mtimeMs||f===0)&&eu(i.listeners,h=>h(t,o))})},tu.set(e,i)),()=>{Te(i,ge,n),Te(i,Be,s),tn(i.listeners)&&(tu.delete(e),ne.unwatchFile(e),i.options=i.watcher=void 0,Object.freeze(i))}},"setFsWatchFileListener");let ua=class{static{a(this,"NodeFsHandler")}constructor(e){this.fsw=e,this._boundHandleError=u=>e._handleError(u)}_watchWithNodeFs(e,u){const r=this.fsw.options,n=I.dirname(e),s=I.basename(e);this.fsw._getWatchedDir(n).add(s);const D=I.resolve(e),o={persistent:r.persistent};u||(u=Mo);let c;return r.usePolling?(o.interval=r.enableBinaryInterval&&Lo(s)?r.binaryInterval:r.interval,c=ta(e,D,o,{listener:u,rawEmitter:this.fsw._emitRaw})):c=ea(e,D,o,{listener:u,errHandler:this._boundHandleError,rawEmitter:this.fsw._emitRaw}),c}_handleFile(e,u,r){if(this.fsw.closed)return;const n=I.dirname(e),s=I.basename(e),i=this.fsw._getWatchedDir(n);let D=u;if(i.has(s))return;const o=a(async(f,h)=>{if(this.fsw._throttle(Yo,e,5)){if(!h||h.mtimeMs===0)try{const l=await en(e);if(this.fsw.closed)return;const p=l.atimeMs,C=l.mtimeMs;(!p||p<=C||C!==D.mtimeMs)&&this.fsw._emit(ut,e,l),ko&&D.ino!==l.ino?(this.fsw._closeFile(f),D=l,this.fsw._addPathCloser(f,this._watchWithNodeFs(e,o))):D=l}catch{this.fsw._remove(n,s)}else if(i.has(s)){const l=h.atimeMs,p=h.mtimeMs;(!l||l<=p||p!==D.mtimeMs)&&this.fsw._emit(ut,e,h),D=h}}},"listener"),c=this._watchWithNodeFs(e,o);if(!(r&&this.fsw.options.ignoreInitial)&&this.fsw._isntIgnored(e)){if(!this.fsw._throttle(rt,e,0))return;this.fsw._emit(rt,e,u)}return c}async _handleSymlink(e,u,r,n){if(this.fsw.closed)return;const s=e.fullPath,i=this.fsw._getWatchedDir(u);if(!this.fsw.options.followSymlinks){this.fsw._incrReadyCount();let D;try{D=await Jt(r)}catch{return this.fsw._emitReady(),!0}return this.fsw.closed?void 0:(i.has(n)?this.fsw._symlinkPaths.get(s)!==D&&(this.fsw._symlinkPaths.set(s,D),this.fsw._emit(ut,r,e.stats)):(i.add(n),this.fsw._symlinkPaths.set(s,D),this.fsw._emit(rt,r,e.stats)),this.fsw._emitReady(),!0)}if(this.fsw._symlinkPaths.has(s))return!0;this.fsw._symlinkPaths.set(s,!0)}_handleRead(e,u,r,n,s,i,D){if(e=I.join(e,Go),!r.hasGlob&&(D=this.fsw._throttle("readdir",e,1e3),!D))return;const o=this.fsw._getWatchedDir(r.path),c=new Set;let f=this.fsw._readdirp(e,{fileFilter:a(h=>r.filterPath(h),"fileFilter"),directoryFilter:a(h=>r.filterDir(h),"directoryFilter"),depth:0}).on(Uo,async h=>{if(this.fsw.closed){f=void 0;return}const l=h.path;let p=I.join(e,l);if(c.add(l),!(h.stats.isSymbolicLink()&&await this._handleSymlink(h,e,p,l))){if(this.fsw.closed){f=void 0;return}(l===n||!n&&!o.has(l))&&(this.fsw._incrReadyCount(),p=I.join(s,I.relative(s,p)),this._addToNodeFs(p,u,r,i+1))}}).on(Jr,this._boundHandleError);return new Promise(h=>f.once(Ko,()=>{if(this.fsw.closed){f=void 0;return}const l=D?D.clear():!1;h(),o.getChildren().filter(p=>p!==e&&!c.has(p)&&(!r.hasGlob||r.filterPath({fullPath:I.resolve(e,p)}))).forEach(p=>{this.fsw._remove(e,p)}),f=void 0,l&&this._handleRead(e,!1,r,n,s,i,D)}))}async _handleDir(e,u,r,n,s,i,D){const o=this.fsw._getWatchedDir(I.dirname(e)),c=o.has(I.basename(e));!(r&&this.fsw.options.ignoreInitial)&&!s&&!c&&(!i.hasGlob||i.globFilter(e))&&this.fsw._emit(jo,e,u),o.add(I.basename(e)),this.fsw._getWatchedDir(e);let f,h;const l=this.fsw.options.depth;if((l==null||n<=l)&&!this.fsw._symlinkPaths.has(D)){if(!s&&(await this._handleRead(e,r,i,s,e,n,f),this.fsw.closed))return;h=this._watchWithNodeFs(e,(p,C)=>{C&&C.mtimeMs===0||this._handleRead(p,!1,i,s,e,n,f)})}return h}async _addToNodeFs(e,u,r,n,s){const i=this.fsw._emitReady;if(this.fsw._isIgnored(e)||this.fsw.closed)return i(),!1;const D=this.fsw._getWatchHelpers(e,n);!D.hasGlob&&r&&(D.hasGlob=r.hasGlob,D.globFilter=r.globFilter,D.filterPath=o=>r.filterPath(o),D.filterDir=o=>r.filterDir(o));try{const o=await Zo[D.statMethod](D.watchPath);if(this.fsw.closed)return;if(this.fsw._isIgnored(D.watchPath,o))return i(),!1;const c=this.fsw.options.followSymlinks&&!e.includes(zo)&&!e.includes(Vo);let f;if(o.isDirectory()){const h=I.resolve(e),l=c?await Jt(e):e;if(this.fsw.closed||(f=await this._handleDir(D.watchPath,o,u,n,s,D,l),this.fsw.closed))return;h!==l&&l!==void 0&&this.fsw._symlinkPaths.set(h,l)}else if(o.isSymbolicLink()){const h=c?await Jt(e):e;if(this.fsw.closed)return;const l=I.dirname(D.watchPath);if(this.fsw._getWatchedDir(l).add(D.watchPath),this.fsw._emit(rt,D.watchPath,o),f=await this._handleDir(l,o,u,n,e,D,h),this.fsw.closed)return;h!==void 0&&this.fsw._symlinkPaths.set(I.resolve(e),h)}else f=this._handleFile(D.watchPath,o,u);return i(),this.fsw._addPathCloser(e,f),!1}catch(o){if(this.fsw._handleError(o))return i(),e}}};var ra=ua,uu={exports:{}};const ru=oe,k=z,{promisify:nu}=_e;let me;try{me=he.require("fsevents")}catch(t){process.env.CHOKIDAR_PRINT_FSEVENTS_REQUIRE_ERROR&&console.error(t)}if(me){const t=process.version.match(/v(\d+)\.(\d+)/);if(t&&t[1]&&t[2]){const e=Number.parseInt(t[1],10),u=Number.parseInt(t[2],10);e===8&&u<16&&(me=void 0)}}const{EV_ADD:su,EV_CHANGE:na,EV_ADD_DIR:rn,EV_UNLINK:it,EV_ERROR:sa,STR_DATA:ia,STR_END:Da,FSEVENT_CREATED:oa,FSEVENT_MODIFIED:aa,FSEVENT_DELETED:la,FSEVENT_MOVED:ca,FSEVENT_UNKNOWN:fa,FSEVENT_FLAG_MUST_SCAN_SUBDIRS:ha,FSEVENT_TYPE_FILE:da,FSEVENT_TYPE_DIRECTORY:xe,FSEVENT_TYPE_SYMLINK:nn,ROOT_GLOBSTAR:sn,DIR_SUFFIX:Ea,DOT_SLASH:Dn,FUNCTION_TYPE:iu,EMPTY_FN:pa,IDENTITY_FN:Ca}=tt,Fa=a(t=>isNaN(t)?{}:{depth:t},"Depth"),Du=nu(ru.stat),ga=nu(ru.lstat),on=nu(ru.realpath),ma={stat:Du,lstat:ga},fe=new Map,_a=10,Aa=new Set([69888,70400,71424,72704,73472,131328,131840,262912]),ya=a((t,e)=>({stop:me.watch(t,e)}),"createFSEventsInstance");function wa(t,e,u,r){let n=k.extname(e)?k.dirname(e):e;const s=k.dirname(n);let i=fe.get(n);Ra(s)&&(n=s);const D=k.resolve(t),o=D!==e,c=a((h,l,p)=>{o&&(h=h.replace(e,D)),(h===D||!h.indexOf(D+k.sep))&&u(h,l,p)},"filteredListener");let f=!1;for(const h of fe.keys())if(e.indexOf(k.resolve(h)+k.sep)===0){n=h,i=fe.get(n),f=!0;break}return i||f?i.listeners.add(c):(i={listeners:new Set([c]),rawEmitter:r,watcher:ya(n,(h,l)=>{if(!i.listeners.size||l&ha)return;const p=me.getInfo(h,l);i.listeners.forEach(C=>{C(h,l,p)}),i.rawEmitter(p.event,h,p)})},fe.set(n,i)),()=>{const h=i.listeners;if(h.delete(c),!h.size&&(fe.delete(n),i.watcher))return i.watcher.stop().then(()=>{i.rawEmitter=i.watcher=void 0,Object.freeze(i)})}}a(wa,"setFSEventsListener");const Ra=a(t=>{let e=0;for(const u of fe.keys())if(u.indexOf(t)===0&&(e++,e>=_a))return!0;return!1},"couldConsolidate"),ba=a(()=>me&&fe.size<128,"canUse"),ou=a((t,e)=>{let u=0;for(;!t.indexOf(e)&&(t=k.dirname(t))!==e;)u++;return u},"calcDepth"),an=a((t,e)=>t.type===xe&&e.isDirectory()||t.type===nn&&e.isSymbolicLink()||t.type===da&&e.isFile(),"sameTypes");let va=class{static{a(this,"FsEventsHandler")}constructor(e){this.fsw=e}checkIgnored(e,u){const r=this.fsw._ignoredPaths;if(this.fsw._isIgnored(e,u))return r.add(e),u&&u.isDirectory()&&r.add(e+sn),!0;r.delete(e),r.delete(e+sn)}addOrChange(e,u,r,n,s,i,D,o){const c=s.has(i)?na:su;this.handleEvent(c,e,u,r,n,s,i,D,o)}async checkExists(e,u,r,n,s,i,D,o){try{const c=await Du(e);if(this.fsw.closed)return;an(D,c)?this.addOrChange(e,u,r,n,s,i,D,o):this.handleEvent(it,e,u,r,n,s,i,D,o)}catch(c){c.code==="EACCES"?this.addOrChange(e,u,r,n,s,i,D,o):this.handleEvent(it,e,u,r,n,s,i,D,o)}}handleEvent(e,u,r,n,s,i,D,o,c){if(!(this.fsw.closed||this.checkIgnored(u)))if(e===it){const f=o.type===xe;(f||i.has(D))&&this.fsw._remove(s,D,f)}else{if(e===su){if(o.type===xe&&this.fsw._getWatchedDir(u),o.type===nn&&c.followSymlinks){const h=c.depth===void 0?void 0:ou(r,n)+1;return this._addToFsEvents(u,!1,!0,h)}this.fsw._getWatchedDir(s).add(D)}const f=o.type===xe?e+Ea:e;this.fsw._emit(f,u),f===rn&&this._addToFsEvents(u,!1,!0)}}_watchWithFsEvents(e,u,r,n){if(this.fsw.closed||this.fsw._isIgnored(e))return;const s=this.fsw.options,D=wa(e,u,a(async(o,c,f)=>{if(this.fsw.closed||s.depth!==void 0&&ou(o,u)>s.depth)return;const h=r(k.join(e,k.relative(e,o)));if(n&&!n(h))return;const l=k.dirname(h),p=k.basename(h),C=this.fsw._getWatchedDir(f.type===xe?h:l);if(Aa.has(c)||f.event===fa)if(typeof s.ignored===iu){let g;try{g=await Du(h)}catch{}if(this.fsw.closed||this.checkIgnored(h,g))return;an(f,g)?this.addOrChange(h,o,u,l,C,p,f,s):this.handleEvent(it,h,o,u,l,C,p,f,s)}else this.checkExists(h,o,u,l,C,p,f,s);else switch(f.event){case oa:case aa:return this.addOrChange(h,o,u,l,C,p,f,s);case la:case ca:return this.checkExists(h,o,u,l,C,p,f,s)}},"watchCallback"),this.fsw._emitRaw);return this.fsw._emitReady(),D}async _handleFsEventsSymlink(e,u,r,n){if(!(this.fsw.closed||this.fsw._symlinkPaths.has(u))){this.fsw._symlinkPaths.set(u,!0),this.fsw._incrReadyCount();try{const s=await on(e);if(this.fsw.closed)return;if(this.fsw._isIgnored(s))return this.fsw._emitReady();this.fsw._incrReadyCount(),this._addToFsEvents(s||e,i=>{let D=e;return s&&s!==Dn?D=i.replace(s,e):i!==Dn&&(D=k.join(e,i)),r(D)},!1,n)}catch(s){if(this.fsw._handleError(s))return this.fsw._emitReady()}}}emitAdd(e,u,r,n,s){const i=r(e),D=u.isDirectory(),o=this.fsw._getWatchedDir(k.dirname(i)),c=k.basename(i);D&&this.fsw._getWatchedDir(i),!o.has(c)&&(o.add(c),(!n.ignoreInitial||s===!0)&&this.fsw._emit(D?rn:su,i,u))}initWatch(e,u,r,n){if(this.fsw.closed)return;const s=this._watchWithFsEvents(r.watchPath,k.resolve(e||r.watchPath),n,r.globFilter);this.fsw._addPathCloser(u,s)}async _addToFsEvents(e,u,r,n){if(this.fsw.closed)return;const s=this.fsw.options,i=typeof u===iu?u:Ca,D=this.fsw._getWatchHelpers(e);try{const o=await ma[D.statMethod](D.watchPath);if(this.fsw.closed)return;if(this.fsw._isIgnored(D.watchPath,o))throw null;if(o.isDirectory()){if(D.globFilter||this.emitAdd(i(e),o,i,s,r),n&&n>s.depth)return;this.fsw._readdirp(D.watchPath,{fileFilter:a(c=>D.filterPath(c),"fileFilter"),directoryFilter:a(c=>D.filterDir(c),"directoryFilter"),...Fa(s.depth-(n||0))}).on(ia,c=>{if(this.fsw.closed||c.stats.isDirectory()&&!D.filterPath(c))return;const f=k.join(D.watchPath,c.path),{fullPath:h}=c;if(D.followSymlinks&&c.stats.isSymbolicLink()){const l=s.depth===void 0?void 0:ou(f,k.resolve(D.watchPath))+1;this._handleFsEventsSymlink(f,h,i,l)}else this.emitAdd(f,c.stats,i,s,r)}).on(sa,pa).on(Da,()=>{this.fsw._emitReady()})}else this.emitAdd(D.watchPath,o,i,s,r),this.fsw._emitReady()}catch(o){(!o||this.fsw._handleError(o))&&(this.fsw._emitReady(),this.fsw._emitReady())}if(s.persistent&&r!==!0)if(typeof u===iu)this.initWatch(void 0,e,D,i);else{let o;try{o=await on(D.watchPath)}catch{}this.initWatch(o,e,D,i)}}};uu.exports=va,uu.exports.canUse=ba;var Sa=uu.exports;const{EventEmitter:Ba}=xn,au=oe,S=z,{promisify:ln}=_e,$a=yD,lu=BD.default,Ta=GD,cu=$r,xa=$o,Oa=Rr,Na=ra,cn=Sa,{EV_ALL:fu,EV_READY:Ha,EV_ADD:Dt,EV_CHANGE:Oe,EV_UNLINK:fn,EV_ADD_DIR:Pa,EV_UNLINK_DIR:La,EV_RAW:Ia,EV_ERROR:hu,STR_CLOSE:ka,STR_END:Ma,BACK_SLASH_RE:Ga,DOUBLE_SLASH_RE:hn,SLASH_OR_BACK_SLASH_RE:Wa,DOT_RE:ja,REPLACER_RE:Ua,SLASH:du,SLASH_SLASH:Ka,BRACE_START:Va,BANG:Eu,ONE_DOT:dn,TWO_DOTS:za,GLOBSTAR:Ya,SLASH_GLOBSTAR:pu,ANYMATCH_OPTS:Cu,STRING_TYPE:Fu,FUNCTION_TYPE:qa,EMPTY_STR:gu,EMPTY_FN:Xa,isWindows:Qa,isMacos:Za,isIBMi:Ja}=tt,el=ln(au.stat),tl=ln(au.readdir),mu=a((t=[])=>Array.isArray(t)?t:[t],"arrify"),En=a((t,e=[])=>(t.forEach(u=>{Array.isArray(u)?En(u,e):e.push(u)}),e),"flatten"),pn=a(t=>{const e=En(mu(t));if(!e.every(u=>typeof u===Fu))throw new TypeError(`Non-string provided as watch path: ${e}`);return e.map(Fn)},"unifyPaths"),Cn=a(t=>{let e=t.replace(Ga,du),u=!1;for(e.startsWith(Ka)&&(u=!0);e.match(hn);)e=e.replace(hn,du);return u&&(e=du+e),e},"toUnix"),Fn=a(t=>Cn(S.normalize(Cn(t))),"normalizePathToUnix"),gn=a((t=gu)=>e=>typeof e!==Fu?e:Fn(S.isAbsolute(e)?e:S.join(t,e)),"normalizeIgnored"),ul=a((t,e)=>S.isAbsolute(t)?t:t.startsWith(Eu)?Eu+S.join(e,t.slice(1)):S.join(e,t),"getAbsolutePath"),X=a((t,e)=>t[e]===void 0,"undef");class rl{static{a(this,"DirEntry")}constructor(e,u){this.path=e,this._removeWatcher=u,this.items=new Set}add(e){const{items:u}=this;u&&e!==dn&&e!==za&&u.add(e)}async remove(e){const{items:u}=this;if(!u||(u.delete(e),u.size>0))return;const r=this.path;try{await tl(r)}catch{this._removeWatcher&&this._removeWatcher(S.dirname(r),S.basename(r))}}has(e){const{items:u}=this;if(u)return u.has(e)}getChildren(){const{items:e}=this;if(e)return[...e.values()]}dispose(){this.items.clear(),delete this.path,delete this._removeWatcher,delete this.items,Object.freeze(this)}}const nl="stat",sl="lstat";class il{static{a(this,"WatchHelper")}constructor(e,u,r,n){this.fsw=n,this.path=e=e.replace(Ua,gu),this.watchPath=u,this.fullWatchPath=S.resolve(u),this.hasGlob=u!==e,e===gu&&(this.hasGlob=!1),this.globSymlink=this.hasGlob&&r?void 0:!1,this.globFilter=this.hasGlob?lu(e,void 0,Cu):!1,this.dirParts=this.getDirParts(e),this.dirParts.forEach(s=>{s.length>1&&s.pop()}),this.followSymlinks=r,this.statMethod=r?nl:sl}checkGlobSymlink(e){return this.globSymlink===void 0&&(this.globSymlink=e.fullParentDir===this.fullWatchPath?!1:{realPath:e.fullParentDir,linkPath:this.fullWatchPath}),this.globSymlink?e.fullPath.replace(this.globSymlink.realPath,this.globSymlink.linkPath):e.fullPath}entryPath(e){return S.join(this.watchPath,S.relative(this.watchPath,this.checkGlobSymlink(e)))}filterPath(e){const{stats:u}=e;if(u&&u.isSymbolicLink())return this.filterDir(e);const r=this.entryPath(e);return(this.hasGlob&&typeof this.globFilter===qa?this.globFilter(r):!0)&&this.fsw._isntIgnored(r,u)&&this.fsw._hasReadPermissions(u)}getDirParts(e){if(!this.hasGlob)return[];const u=[];return(e.includes(Va)?xa.expand(e):[e]).forEach(n=>{u.push(S.relative(this.watchPath,n).split(Wa))}),u}filterDir(e){if(this.hasGlob){const u=this.getDirParts(this.checkGlobSymlink(e));let r=!1;this.unmatchedGlob=!this.dirParts.some(n=>n.every((s,i)=>(s===Ya&&(r=!0),r||!u[0][i]||lu(s,u[0][i],Cu))))}return!this.unmatchedGlob&&this.fsw._isntIgnored(this.entryPath(e),e.stats)}}class Dl extends Ba{static{a(this,"FSWatcher")}constructor(e){super();const u={};e&&Object.assign(u,e),this._watched=new Map,this._closers=new Map,this._ignoredPaths=new Set,this._throttled=new Map,this._symlinkPaths=new Map,this._streams=new Set,this.closed=!1,X(u,"persistent")&&(u.persistent=!0),X(u,"ignoreInitial")&&(u.ignoreInitial=!1),X(u,"ignorePermissionErrors")&&(u.ignorePermissionErrors=!1),X(u,"interval")&&(u.interval=100),X(u,"binaryInterval")&&(u.binaryInterval=300),X(u,"disableGlobbing")&&(u.disableGlobbing=!1),u.enableBinaryInterval=u.binaryInterval!==u.interval,X(u,"useFsEvents")&&(u.useFsEvents=!u.usePolling),cn.canUse()||(u.useFsEvents=!1),X(u,"usePolling")&&!u.useFsEvents&&(u.usePolling=Za),Ja&&(u.usePolling=!0);const n=process.env.CHOKIDAR_USEPOLLING;if(n!==void 0){const o=n.toLowerCase();o==="false"||o==="0"?u.usePolling=!1:o==="true"||o==="1"?u.usePolling=!0:u.usePolling=!!o}const s=process.env.CHOKIDAR_INTERVAL;s&&(u.interval=Number.parseInt(s,10)),X(u,"atomic")&&(u.atomic=!u.usePolling&&!u.useFsEvents),u.atomic&&(this._pendingUnlinks=new Map),X(u,"followSymlinks")&&(u.followSymlinks=!0),X(u,"awaitWriteFinish")&&(u.awaitWriteFinish=!1),u.awaitWriteFinish===!0&&(u.awaitWriteFinish={});const i=u.awaitWriteFinish;i&&(i.stabilityThreshold||(i.stabilityThreshold=2e3),i.pollInterval||(i.pollInterval=100),this._pendingWrites=new Map),u.ignored&&(u.ignored=mu(u.ignored));let D=0;this._emitReady=()=>{D++,D>=this._readyCount&&(this._emitReady=Xa,this._readyEmitted=!0,process.nextTick(()=>this.emit(Ha)))},this._emitRaw=(...o)=>this.emit(Ia,...o),this._readyEmitted=!1,this.options=u,u.useFsEvents?this._fsEventsHandler=new cn(this):this._nodeFsHandler=new Na(this),Object.freeze(u)}add(e,u,r){const{cwd:n,disableGlobbing:s}=this.options;this.closed=!1;let i=pn(e);return n&&(i=i.map(D=>{const o=ul(D,n);return s||!cu(D)?o:Oa(o)})),i=i.filter(D=>D.startsWith(Eu)?(this._ignoredPaths.add(D.slice(1)),!1):(this._ignoredPaths.delete(D),this._ignoredPaths.delete(D+pu),this._userIgnored=void 0,!0)),this.options.useFsEvents&&this._fsEventsHandler?(this._readyCount||(this._readyCount=i.length),this.options.persistent&&(this._readyCount+=i.length),i.forEach(D=>this._fsEventsHandler._addToFsEvents(D))):(this._readyCount||(this._readyCount=0),this._readyCount+=i.length,Promise.all(i.map(async D=>{const o=await this._nodeFsHandler._addToNodeFs(D,!r,0,0,u);return o&&this._emitReady(),o})).then(D=>{this.closed||D.filter(o=>o).forEach(o=>{this.add(S.dirname(o),S.basename(u||o))})})),this}unwatch(e){if(this.closed)return this;const u=pn(e),{cwd:r}=this.options;return u.forEach(n=>{!S.isAbsolute(n)&&!this._closers.has(n)&&(r&&(n=S.join(r,n)),n=S.resolve(n)),this._closePath(n),this._ignoredPaths.add(n),this._watched.has(n)&&this._ignoredPaths.add(n+pu),this._userIgnored=void 0}),this}close(){if(this.closed)return this._closePromise;this.closed=!0,this.removeAllListeners();const e=[];return this._closers.forEach(u=>u.forEach(r=>{const n=r();n instanceof Promise&&e.push(n)})),this._streams.forEach(u=>u.destroy()),this._userIgnored=void 0,this._readyCount=0,this._readyEmitted=!1,this._watched.forEach(u=>u.dispose()),["closers","watched","streams","symlinkPaths","throttled"].forEach(u=>{this[`_${u}`].clear()}),this._closePromise=e.length?Promise.all(e).then(()=>{}):Promise.resolve(),this._closePromise}getWatched(){const e={};return this._watched.forEach((u,r)=>{const n=this.options.cwd?S.relative(this.options.cwd,r):r;e[n||dn]=u.getChildren().sort()}),e}emitWithAll(e,u){this.emit(...u),e!==hu&&this.emit(fu,...u)}async _emit(e,u,r,n,s){if(this.closed)return;const i=this.options;Qa&&(u=S.normalize(u)),i.cwd&&(u=S.relative(i.cwd,u));const D=[e,u];s!==void 0?D.push(r,n,s):n!==void 0?D.push(r,n):r!==void 0&&D.push(r);const o=i.awaitWriteFinish;let c;if(o&&(c=this._pendingWrites.get(u)))return c.lastChange=new Date,this;if(i.atomic){if(e===fn)return this._pendingUnlinks.set(u,D),setTimeout(()=>{this._pendingUnlinks.forEach((f,h)=>{this.emit(...f),this.emit(fu,...f),this._pendingUnlinks.delete(h)})},typeof i.atomic=="number"?i.atomic:100),this;e===Dt&&this._pendingUnlinks.has(u)&&(e=D[0]=Oe,this._pendingUnlinks.delete(u))}if(o&&(e===Dt||e===Oe)&&this._readyEmitted){const f=a((h,l)=>{h?(e=D[0]=hu,D[1]=h,this.emitWithAll(e,D)):l&&(D.length>2?D[2]=l:D.push(l),this.emitWithAll(e,D))},"awfEmit");return this._awaitWriteFinish(u,o.stabilityThreshold,e,f),this}if(e===Oe&&!this._throttle(Oe,u,50))return this;if(i.alwaysStat&&r===void 0&&(e===Dt||e===Pa||e===Oe)){const f=i.cwd?S.join(i.cwd,u):u;let h;try{h=await el(f)}catch{}if(!h||this.closed)return;D.push(h)}return this.emitWithAll(e,D),this}_handleError(e){const u=e&&e.code;return e&&u!=="ENOENT"&&u!=="ENOTDIR"&&(!this.options.ignorePermissionErrors||u!=="EPERM"&&u!=="EACCES")&&this.emit(hu,e),e||this.closed}_throttle(e,u,r){this._throttled.has(e)||this._throttled.set(e,new Map);const n=this._throttled.get(e),s=n.get(u);if(s)return s.count++,!1;let i;const D=a(()=>{const c=n.get(u),f=c?c.count:0;return n.delete(u),clearTimeout(i),c&&clearTimeout(c.timeoutObject),f},"clear");i=setTimeout(D,r);const o={timeoutObject:i,clear:D,count:0};return n.set(u,o),o}_incrReadyCount(){return this._readyCount++}_awaitWriteFinish(e,u,r,n){let s,i=e;this.options.cwd&&!S.isAbsolute(e)&&(i=S.join(this.options.cwd,e));const D=new Date,o=a(c=>{au.stat(i,(f,h)=>{if(f||!this._pendingWrites.has(e)){f&&f.code!=="ENOENT"&&n(f);return}const l=Number(new Date);c&&h.size!==c.size&&(this._pendingWrites.get(e).lastChange=l);const p=this._pendingWrites.get(e);l-p.lastChange>=u?(this._pendingWrites.delete(e),n(void 0,h)):s=setTimeout(o,this.options.awaitWriteFinish.pollInterval,h)})},"awaitWriteFinish");this._pendingWrites.has(e)||(this._pendingWrites.set(e,{lastChange:D,cancelWait:a(()=>(this._pendingWrites.delete(e),clearTimeout(s),r),"cancelWait")}),s=setTimeout(o,this.options.awaitWriteFinish.pollInterval))}_getGlobIgnored(){return[...this._ignoredPaths.values()]}_isIgnored(e,u){if(this.options.atomic&&ja.test(e))return!0;if(!this._userIgnored){const{cwd:r}=this.options,n=this.options.ignored,s=n&&n.map(gn(r)),i=mu(s).filter(o=>typeof o===Fu&&!cu(o)).map(o=>o+pu),D=this._getGlobIgnored().map(gn(r)).concat(s,i);this._userIgnored=lu(D,void 0,Cu)}return this._userIgnored([e,u])}_isntIgnored(e,u){return!this._isIgnored(e,u)}_getWatchHelpers(e,u){const r=u||this.options.disableGlobbing||!cu(e)?e:Ta(e),n=this.options.followSymlinks;return new il(e,r,n,this)}_getWatchedDir(e){this._boundRemove||(this._boundRemove=this._remove.bind(this));const u=S.resolve(e);return this._watched.has(u)||this._watched.set(u,new rl(u,this._boundRemove)),this._watched.get(u)}_hasReadPermissions(e){if(this.options.ignorePermissionErrors)return!0;const r=(e&&Number.parseInt(e.mode,10))&511;return!!(4&Number.parseInt(r.toString(8)[0],10))}_remove(e,u,r){const n=S.join(e,u),s=S.resolve(n);if(r=r??(this._watched.has(n)||this._watched.has(s)),!this._throttle("remove",n,100))return;!r&&!this.options.useFsEvents&&this._watched.size===1&&this.add(e,u,!0),this._getWatchedDir(n).getChildren().forEach(l=>this._remove(n,l));const o=this._getWatchedDir(e),c=o.has(u);o.remove(u),this._symlinkPaths.has(s)&&this._symlinkPaths.delete(s);let f=n;if(this.options.cwd&&(f=S.relative(this.options.cwd,n)),this.options.awaitWriteFinish&&this._pendingWrites.has(f)&&this._pendingWrites.get(f).cancelWait()===Dt)return;this._watched.delete(n),this._watched.delete(s);const h=r?La:fn;c&&!this._isIgnored(n)&&this._emit(h,n),this.options.useFsEvents||this._closePath(n)}_closePath(e){this._closeFile(e);const u=S.dirname(e);this._getWatchedDir(u).remove(S.basename(e))}_closeFile(e){const u=this._closers.get(e);u&&(u.forEach(r=>r()),this._closers.delete(e))}_addPathCloser(e,u){if(!u)return;let r=this._closers.get(e);r||(r=[],this._closers.set(e,r)),r.push(u)}_readdirp(e,u){if(this.closed)return;const r={type:fu,alwaysStat:!0,lstat:!0,...u};let n=$a(e,r);return this._streams.add(n),n.once(ka,()=>{n=void 0}),n.once(Ma,()=>{n&&(this._streams.delete(n),n=void 0)}),n}}const ol=a((t,e)=>{const u=new Dl(e);return u.add(t),u},"watch");var al=ol;const ot=a((t=!0)=>{let e=!1;return u=>{if(e||u==="unknown-flag")return!0;if(u==="argument")return e=!0,t}},"ignoreAfterArgument"),mn=a((t,e=process.argv.slice(2))=>(wu(t,e,{ignore:ot()}),e),"removeArgvFlags"),ll=a(t=>{let e=Buffer.alloc(0);return u=>{for(e=Buffer.concat([e,u]);e.length>4;){const r=e.readInt32BE(0);if(e.length>=4+r){const n=e.slice(4,4+r);t(n),e=e.slice(4+r)}else break}}},"bufferData"),_n=a(async()=>{const t=Nn.createServer(u=>{u.on("data",ll(r=>{const n=JSON.parse(r.toString());t.emit("data",n)}))}),e=he.getPipePath(process.pid);return await ft.promises.mkdir(Hn.tmpdir,{recursive:!0}),await ft.promises.rm(e,{force:!0}),await new Promise((u,r)=>{t.listen(e,u),t.on("error",r)}),t.unref(),process.on("exit",()=>{if(t.close(),!he.isWindows)try{ft.rmSync(e)}catch{}}),t},"createIpcServer"),cl=a(()=>new Date().toLocaleTimeString(),"currentTime"),Ne=a((...t)=>console.log(ue.gray(cl()),ue.lightCyan("[tsx]"),...t),"log"),fl="\x1Bc",hl=a((t,e)=>{let u;return function(){u&&clearTimeout(u),u=setTimeout(()=>Reflect.apply(t,this,arguments),e)}},"debounce"),An={noCache:{type:Boolean,description:"Disable caching",default:!1},tsconfig:{type:String,description:"Custom tsconfig.json path"},clearScreen:{type:Boolean,description:"Clearing the screen on rerun",default:!0},ignore:{type:[String],description:"Paths & globs to exclude from being watched (Deprecated: use --exclude)"},include:{type:[String],description:"Additional paths & globs to watch"},exclude:{type:[String],description:"Paths & globs to exclude from being watched"}},dl=Js({name:"watch",parameters:["<script path>"],flags:An,help:{description:"Run the script and watch for changes"},ignoreArgv:ot(!1)},async t=>{const e=mn(An,process.argv.slice(3)),u={noCache:t.flags.noCache,tsconfigPath:t.flags.tsconfig,clearScreen:t.flags.clearScreen,include:t.flags.include,exclude:[...t.flags.ignore,...t.flags.exclude],ipc:!0};let r,n=!1;(await _n()).on("data",l=>{if(l&&typeof l=="object"&&"type"in l&&l.type==="dependency"&&"path"in l&&typeof l.path=="string"){const p=l.path.startsWith("file:")?_u.fileURLToPath(l.path):l.path;Tn.isAbsolute(p)&&h.add(p)}});const i=a(()=>{if(!n)return ir(e,u)},"spawnProcess");let D=!1;const o=a(async(l,p="SIGTERM",C=5e3)=>{let g=!1;const y=new Promise(B=>{l.on("exit",H=>{g=!0,D=!1,B(H)})});return D=!0,l.kill(p),setTimeout(()=>{g||(Ne(ue.yellow(`Process didn't exit in ${Math.floor(C/1e3)}s. Force killing...`)),l.kill("SIGKILL"))},C),await y},"killProcess"),c=hl(async(l,p)=>{const C=l?`${l?ue.lightMagenta(l):""}${p?` in ${ue.lightGreen(`./${p}`)}`:""}`:"";if(D){Ne(C,ue.yellow("Process hasn't exited. Killing process...")),r.kill("SIGKILL");return}r&&(r.exitCode===null?(Ne(C,ue.yellow("Restarting...")),await o(r)):Ne(C,ue.yellow("Rerunning...")),u.clearScreen&&process.stdout.write(fl)),r=i()},100);c();const f=a(l=>{n=!0,r?.exitCode===null?(D&&Ne(ue.yellow("Previous process hasn't exited yet. Force killing...")),o(r,D?"SIGKILL":l).then(p=>{process.exit(p??0)},()=>{})):process.exit(ct.constants.signals[l])},"relaySignal");process.on("SIGINT",f),process.on("SIGTERM",f);const h=al([...t._,...u.include],{cwd:process.cwd(),ignoreInitial:!0,ignored:["**/.*/**","**/.*","**/{node_modules,bower_components,vendor}/**",...u.exclude],ignorePermissionErrors:!0}).on("all",c);process.stdin.on("data",()=>c("Return key"))}),El=a((t,e)=>{let u;e.on("data",s=>{s&&s.type==="signal"&&u&&u(s.signal)});const r=a(()=>{const s=new Promise(i=>{setTimeout(()=>i(void 0),30),u=i});return s.then(()=>{u=void 0},()=>{}),s},"waitForSignalFromChild"),n=a(async s=>{await r()!==s&&(t.kill(s),await r()!==s&&(t.on("exit",()=>{const o=ct.constants.signals[s];process.exit(128+o)}),t.kill("SIGKILL")))},"relaySignalToChild");process.on("SIGINT",n),process.on("SIGTERM",n)},"relaySignals"),yn={noCache:{type:Boolean,description:"Disable caching"},tsconfig:{type:String,description:"Custom tsconfig.json path"}};Uu({name:"tsx",parameters:["[script path]"],commands:[dl],flags:{...yn,version:{type:Boolean,alias:"v",description:"Show version"},help:{type:Boolean,alias:"h",description:"Show help"}},help:!1,ignoreArgv:ot()},async t=>{t.flags.version?process.stdout.write(`tsx v${Bn.version}
node `):t.flags.help&&(t.showHelp({description:"Node.js runtime enhanced with esbuild for loading TypeScript & ESM"}),console.log(`${"-".repeat(45)}
`));const e={eval:{type:String,alias:"e"},print:{type:String,alias:"p"}},{_:u,flags:r}=Uu({flags:{...e,inputType:String,test:Boolean},help:!1,ignoreArgv:ot(!1)}),n=mn({...yn,...e}),i=["print","eval"].find(c=>!!r[c]);if(i){const{inputType:c}=r,f=r[i],h=Sn.transformSync(f,{loader:"default",sourcefile:"/eval.ts",format:c==="module"?"esm":"cjs"});n.unshift(`--${i}`,h.code)}ke.isFeatureSupported(ke.testRunnerGlob)&&r.test&&u.length===0&&n.push("**/{test,test/**/*,test-*,*[.-_]test}.?(c|m)@(t|j)s");const D=await _n(),o=ir(n,{noCache:!!t.flags.noCache,tsconfigPath:t.flags.tsconfig});El(o,D),process.send&&o.on("message",c=>{process.send(c)}),o.send&&process.on("message",c=>{o.send(c)}),o.on("close",c=>{c===null&&(c=ct.constants.signals[o.signalCode]+128),process.exit(c)})});
