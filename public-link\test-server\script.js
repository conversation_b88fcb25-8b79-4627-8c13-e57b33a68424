// Public-Link Test Server JavaScript
console.log('🚀 Public-Link Test Server JavaScript loaded!');

// Set timestamp when page loads
document.addEventListener('DOMContentLoaded', function() {
    console.log('✅ DOM ready - test server is working!');
    
    // Set current timestamp
    const timestampElement = document.getElementById('timestamp');
    if (timestampElement) {
        const now = new Date();
        timestampElement.textContent = now.toLocaleString();
    }
    
    // Add test button functionality
    const testBtn = document.getElementById('testBtn');
    const testResult = document.getElementById('testResult');
    
    if (testBtn && testResult) {
        testBtn.addEventListener('click', function() {
            console.log('🧪 Test button clicked!');
            
            // Show loading state
            testResult.className = 'test-result info';
            testResult.textContent = '⏳ Running JavaScript test...';
            
            // Simulate some processing
            setTimeout(() => {
                // Generate random test data
                const testData = {
                    timestamp: new Date().toISOString(),
                    randomNumber: Math.floor(Math.random() * 1000),
                    userAgent: navigator.userAgent.substring(0, 50) + '...',
                    screenSize: `${screen.width}x${screen.height}`,
                    language: navigator.language
                };
                
                // Show success result
                testResult.className = 'test-result success';
                testResult.innerHTML = `
                    ✅ JavaScript Test Successful!<br>
                    🎲 Random: ${testData.randomNumber}<br>
                    📱 Screen: ${testData.screenSize}<br>
                    🌍 Language: ${testData.language}
                `;
                
                console.log('✅ Test completed:', testData);
                
                // Add some visual feedback
                testBtn.textContent = '✅ Test Completed!';
                setTimeout(() => {
                    testBtn.textContent = 'Click to Test JavaScript';
                }, 2000);
                
            }, 1000);
        });
    }
    
    // Add some interactive features
    addInteractiveFeatures();
});

function addInteractiveFeatures() {
    // Add hover effects to feature cards
    const featureCards = document.querySelectorAll('.feature-card');
    featureCards.forEach((card, index) => {
        card.addEventListener('mouseenter', function() {
            console.log(`🎯 Hovering over feature card ${index + 1}`);
        });
    });
    
    // Add click counter
    let clickCount = 0;
    document.addEventListener('click', function() {
        clickCount++;
        console.log(`👆 Total clicks: ${clickCount}`);
    });
    
    // Log page performance
    window.addEventListener('load', function() {
        setTimeout(() => {
            const perfData = performance.getEntriesByType('navigation')[0];
            console.log('📊 Page Performance:', {
                loadTime: Math.round(perfData.loadEventEnd - perfData.loadEventStart),
                domContentLoaded: Math.round(perfData.domContentLoadedEventEnd - perfData.domContentLoadedEventStart),
                totalTime: Math.round(perfData.loadEventEnd - perfData.fetchStart)
            });
        }, 100);
    });
}

// Export some test functions for debugging
window.testServer = {
    getInfo: function() {
        return {
            title: document.title,
            url: window.location.href,
            timestamp: new Date().toISOString(),
            ready: true
        };
    },
    
    runTest: function() {
        const testBtn = document.getElementById('testBtn');
        if (testBtn) {
            testBtn.click();
            return 'Test triggered!';
        }
        return 'Test button not found';
    },
    
    log: function(message) {
        console.log('🔧 Test Server:', message);
        return 'Logged: ' + message;
    }
};

console.log('🎉 Test server fully initialized!');
console.log('💡 Try: testServer.getInfo() in console');
