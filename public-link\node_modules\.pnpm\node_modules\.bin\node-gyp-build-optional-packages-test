#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/Desktop/websites/New folder/public-link/node_modules/.pnpm/node-gyp-build-optional-packages@5.2.2/node_modules/node-gyp-build-optional-packages/node_modules:/mnt/c/Users/<USER>/Desktop/websites/New folder/public-link/node_modules/.pnpm/node-gyp-build-optional-packages@5.2.2/node_modules:/mnt/c/Users/<USER>/Desktop/websites/New folder/public-link/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/Desktop/websites/New folder/public-link/node_modules/.pnpm/node-gyp-build-optional-packages@5.2.2/node_modules/node-gyp-build-optional-packages/node_modules:/mnt/c/Users/<USER>/Desktop/websites/New folder/public-link/node_modules/.pnpm/node-gyp-build-optional-packages@5.2.2/node_modules:/mnt/c/Users/<USER>/Desktop/websites/New folder/public-link/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../node-gyp-build-optional-packages/build-test.js" "$@"
else
  exec node  "$basedir/../node-gyp-build-optional-packages/build-test.js" "$@"
fi
