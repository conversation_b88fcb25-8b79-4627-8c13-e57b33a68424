import { useState, useEffect } from 'react';

interface Platform {
  name: string;
  description: string;
  available: boolean;
  supportsBackend: boolean;
  cost: string;
}

export default function App() {
  const [localhostUrl, setLocalhostUrl] = useState('');
  const [selectedPlatform, setSelectedPlatform] = useState('auto');
  const [platforms, setPlatforms] = useState<Record<string, Platform>>({});
  const [jobId, setJobId] = useState('');
  const [status, setStatus] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  // Fetch available platforms on component mount
  useEffect(() => {
    fetch('http://localhost:4000/platforms')
      .then(res => res.json())
      .then(data => {
        setPlatforms(data.platforms);
        // Set default platform based on recommendation
        if (data.recommendation?.backend) {
          setSelectedPlatform('auto');
        }
      })
      .catch(err => console.error('Failed to fetch platforms:', err));
  }, []);

  async function handleDeploy() {
    if (!localhostUrl.trim()) return;
    setLoading(true);

    try {
      // Send the localhost URL and platform to the API
      const res = await fetch('http://localhost:4000/deploy-url', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          url: localhostUrl.trim(),
          platform: selectedPlatform
        })
      });

      if (!res.ok) {
        throw new Error(`Failed to deploy: ${res.statusText}`);
      }

      const { jobId } = await res.json();
      setJobId(jobId);

      // Poll for status updates
      const poll = setInterval(async () => {
        const r = await fetch(`http://localhost:4000/status/${jobId}`);
        if (r.ok) {
          const j = await r.json();
          setStatus(j);
          if (j.status === 'completed' || j.status === 'failed') {
            clearInterval(poll);
            setLoading(false);
          }
        }
      }, 2000);
    } catch (error) {
      console.error('Deploy error:', error);
      setStatus({ error: error instanceof Error ? error.message : 'An error occurred' });
      setLoading(false);
    }
  }

  return (
    <div className="p-8 space-y-4 max-w-2xl mx-auto">
      <h1 className="text-3xl font-bold">Public‑Link</h1>
      <p className="text-gray-600">Turn your localhost project into a shareable public URL</p>

      <div className="space-y-2">
        <label className="block text-sm font-medium">Localhost URL:</label>
        <input
          type="url"
          placeholder="http://localhost:3000"
          value={localhostUrl}
          onChange={e => setLocalhostUrl(e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
      </div>

      <div className="space-y-2">
        <label className="block text-sm font-medium">Deployment Platform:</label>
        <select
          value={selectedPlatform}
          onChange={e => setSelectedPlatform(e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="auto">🤖 Auto-detect (Recommended)</option>
          {Object.entries(platforms).map(([key, platform]) => (
            <option key={key} value={key} disabled={!platform.available}>
              {platform.available ? '✅' : '❌'} {platform.name} - {platform.description}
              {!platform.available && ' (Not configured)'}
            </option>
          ))}
        </select>

        {selectedPlatform !== 'auto' && platforms[selectedPlatform] && (
          <div className="text-sm text-gray-600 bg-gray-50 p-2 rounded">
            <strong>{platforms[selectedPlatform].name}:</strong> {platforms[selectedPlatform].description}
            <br />
            <span className="text-xs">
              Backend Support: {platforms[selectedPlatform].supportsBackend ? '✅ Yes' : '❌ No'} |
              Cost: {platforms[selectedPlatform].cost}
            </span>
          </div>
        )}

        {selectedPlatform === 'auto' && (
          <div className="text-sm text-gray-600 bg-blue-50 p-2 rounded">
            🤖 <strong>Auto-detect:</strong> Will automatically choose the best platform based on your application type.
            Static apps → GitHub Pages | Backend apps → Railway/Vercel (if configured)
          </div>
        )}
      </div>

      <button
        className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
        onClick={handleDeploy}
        disabled={!localhostUrl.trim() || loading}
      >
        {loading ? 'Generating...' : 'Generate Public Link'}
      </button>

      {jobId && (
        <div className="p-4 bg-blue-50 rounded-md">
          <p className="text-sm text-blue-800">Job ID: {jobId}</p>
          {loading && <p className="text-sm text-blue-600">Processing your localhost project...</p>}
        </div>
      )}

      {status && (
        <div className="p-4 bg-gray-50 rounded-md">
          <h3 className="font-medium mb-2">Status:</h3>

          {status.status === 'completed' && status.result ? (
            <div className="space-y-3">
              <div className="p-3 bg-green-100 border border-green-300 rounded-md">
                <h4 className="font-medium text-green-800 mb-2">✅ Deployment Successful!</h4>
                <p className="text-sm text-green-700">{status.result.message}</p>
                {status.result.platform && (
                  <p className="text-sm text-green-600 mt-1">
                    🚀 Platform: <strong>{status.result.platform}</strong>
                    {status.result.hasBackend && (
                      <span className="ml-2">
                        {status.result.platform === 'github-pages' ? '⚠️ Backend simulated' : '✅ Full backend support'}
                      </span>
                    )}
                  </p>
                )}
                {status.result.note && (
                  <p className="text-sm text-green-600 mt-1">📝 {status.result.note}</p>
                )}
              </div>

              <div className="space-y-2">
                <div>
                  <label className="block text-sm font-medium text-gray-700">🌐 Live Website:</label>
                  <a
                    href={status.result.githubPages}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-600 hover:text-blue-800 underline break-all"
                  >
                    {status.result.githubPages}
                  </a>
                  {status.result.pagesEnabled === false && (
                    <p className="text-sm text-orange-600 mt-1">⚠️ GitHub Pages needs to be manually enabled</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">📁 GitHub Repository:</label>
                  <a
                    href={status.result.githubRepo}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-600 hover:text-blue-800 underline break-all"
                  >
                    {status.result.githubRepo}
                  </a>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">🔗 Original URL:</label>
                  <span className="text-gray-600">{status.result.originalUrl}</span>
                </div>
              </div>
            </div>
          ) : (
            <pre className="text-sm overflow-auto">{JSON.stringify(status, null, 2)}</pre>
          )}
        </div>
      )}
    </div>
  );
}
