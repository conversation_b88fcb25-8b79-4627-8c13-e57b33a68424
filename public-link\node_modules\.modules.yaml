hoistPattern:
  - '*'
hoistedDependencies:
  '@ampproject/remapping@2.3.0':
    '@ampproject/remapping': private
  '@babel/code-frame@7.27.1':
    '@babel/code-frame': private
  '@babel/compat-data@7.27.5':
    '@babel/compat-data': private
  '@babel/core@7.27.4':
    '@babel/core': private
  '@babel/generator@7.27.5':
    '@babel/generator': private
  '@babel/helper-compilation-targets@7.27.2':
    '@babel/helper-compilation-targets': private
  '@babel/helper-module-imports@7.27.1':
    '@babel/helper-module-imports': private
  '@babel/helper-module-transforms@7.27.3(@babel/core@7.27.4)':
    '@babel/helper-module-transforms': private
  '@babel/helper-plugin-utils@7.27.1':
    '@babel/helper-plugin-utils': private
  '@babel/helper-string-parser@7.27.1':
    '@babel/helper-string-parser': private
  '@babel/helper-validator-identifier@7.27.1':
    '@babel/helper-validator-identifier': private
  '@babel/helper-validator-option@7.27.1':
    '@babel/helper-validator-option': private
  '@babel/helpers@7.27.6':
    '@babel/helpers': private
  '@babel/parser@7.27.5':
    '@babel/parser': private
  '@babel/plugin-transform-react-jsx-self@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-react-jsx-self': private
  '@babel/plugin-transform-react-jsx-source@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-react-jsx-source': private
  '@babel/runtime@7.27.6':
    '@babel/runtime': private
  '@babel/template@7.27.2':
    '@babel/template': private
  '@babel/traverse@7.27.4':
    '@babel/traverse': private
  '@babel/types@7.27.6':
    '@babel/types': private
  '@cspotcode/source-map-support@0.8.1':
    '@cspotcode/source-map-support': private
  '@esbuild/aix-ppc64@0.25.5':
    '@esbuild/aix-ppc64': private
  '@esbuild/android-arm64@0.25.5':
    '@esbuild/android-arm64': private
  '@esbuild/android-arm@0.25.5':
    '@esbuild/android-arm': private
  '@esbuild/android-x64@0.25.5':
    '@esbuild/android-x64': private
  '@esbuild/darwin-arm64@0.25.5':
    '@esbuild/darwin-arm64': private
  '@esbuild/darwin-x64@0.25.5':
    '@esbuild/darwin-x64': private
  '@esbuild/freebsd-arm64@0.25.5':
    '@esbuild/freebsd-arm64': private
  '@esbuild/freebsd-x64@0.25.5':
    '@esbuild/freebsd-x64': private
  '@esbuild/linux-arm64@0.25.5':
    '@esbuild/linux-arm64': private
  '@esbuild/linux-arm@0.25.5':
    '@esbuild/linux-arm': private
  '@esbuild/linux-ia32@0.25.5':
    '@esbuild/linux-ia32': private
  '@esbuild/linux-loong64@0.25.5':
    '@esbuild/linux-loong64': private
  '@esbuild/linux-mips64el@0.25.5':
    '@esbuild/linux-mips64el': private
  '@esbuild/linux-ppc64@0.25.5':
    '@esbuild/linux-ppc64': private
  '@esbuild/linux-riscv64@0.25.5':
    '@esbuild/linux-riscv64': private
  '@esbuild/linux-s390x@0.25.5':
    '@esbuild/linux-s390x': private
  '@esbuild/linux-x64@0.25.5':
    '@esbuild/linux-x64': private
  '@esbuild/netbsd-arm64@0.25.5':
    '@esbuild/netbsd-arm64': private
  '@esbuild/netbsd-x64@0.25.5':
    '@esbuild/netbsd-x64': private
  '@esbuild/openbsd-arm64@0.25.5':
    '@esbuild/openbsd-arm64': private
  '@esbuild/openbsd-x64@0.25.5':
    '@esbuild/openbsd-x64': private
  '@esbuild/sunos-x64@0.25.5':
    '@esbuild/sunos-x64': private
  '@esbuild/win32-arm64@0.25.5':
    '@esbuild/win32-arm64': private
  '@esbuild/win32-ia32@0.25.5':
    '@esbuild/win32-ia32': private
  '@esbuild/win32-x64@0.25.5':
    '@esbuild/win32-x64': private
  '@eslint-community/eslint-utils@4.7.0(eslint@9.29.0)':
    '@eslint-community/eslint-utils': private
  '@eslint-community/regexpp@4.12.1':
    '@eslint-community/regexpp': private
  '@eslint/config-array@0.20.1':
    '@eslint/config-array': private
  '@eslint/config-helpers@0.2.3':
    '@eslint/config-helpers': private
  '@eslint/core@0.14.0':
    '@eslint/core': private
  '@eslint/eslintrc@3.3.1':
    '@eslint/eslintrc': private
  '@eslint/js@9.29.0':
    '@eslint/js': private
  '@eslint/object-schema@2.1.6':
    '@eslint/object-schema': private
  '@eslint/plugin-kit@0.3.2':
    '@eslint/plugin-kit': private
  '@humanfs/core@0.19.1':
    '@humanfs/core': private
  '@humanfs/node@0.16.6':
    '@humanfs/node': private
  '@humanwhocodes/module-importer@1.0.1':
    '@humanwhocodes/module-importer': private
  '@humanwhocodes/retry@0.4.3':
    '@humanwhocodes/retry': private
  '@ioredis/commands@1.2.0':
    '@ioredis/commands': private
  '@jridgewell/gen-mapping@0.3.8':
    '@jridgewell/gen-mapping': private
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': private
  '@jridgewell/set-array@1.2.1':
    '@jridgewell/set-array': private
  '@jridgewell/sourcemap-codec@1.5.0':
    '@jridgewell/sourcemap-codec': private
  '@jridgewell/trace-mapping@0.3.9':
    '@jridgewell/trace-mapping': private
  '@msgpackr-extract/msgpackr-extract-darwin-arm64@3.0.3':
    '@msgpackr-extract/msgpackr-extract-darwin-arm64': private
  '@msgpackr-extract/msgpackr-extract-darwin-x64@3.0.3':
    '@msgpackr-extract/msgpackr-extract-darwin-x64': private
  '@msgpackr-extract/msgpackr-extract-linux-arm64@3.0.3':
    '@msgpackr-extract/msgpackr-extract-linux-arm64': private
  '@msgpackr-extract/msgpackr-extract-linux-arm@3.0.3':
    '@msgpackr-extract/msgpackr-extract-linux-arm': private
  '@msgpackr-extract/msgpackr-extract-linux-x64@3.0.3':
    '@msgpackr-extract/msgpackr-extract-linux-x64': private
  '@msgpackr-extract/msgpackr-extract-win32-x64@3.0.3':
    '@msgpackr-extract/msgpackr-extract-win32-x64': private
  '@nodelib/fs.scandir@2.1.5':
    '@nodelib/fs.scandir': private
  '@nodelib/fs.stat@2.0.5':
    '@nodelib/fs.stat': private
  '@nodelib/fs.walk@1.2.8':
    '@nodelib/fs.walk': private
  '@octokit/auth-token@4.0.0':
    '@octokit/auth-token': private
  '@octokit/core@5.2.1':
    '@octokit/core': private
  '@octokit/endpoint@9.0.6':
    '@octokit/endpoint': private
  '@octokit/graphql@7.1.1':
    '@octokit/graphql': private
  '@octokit/openapi-types@24.2.0':
    '@octokit/openapi-types': private
  '@octokit/plugin-paginate-rest@11.4.4-cjs.2(@octokit/core@5.2.1)':
    '@octokit/plugin-paginate-rest': private
  '@octokit/plugin-request-log@4.0.1(@octokit/core@5.2.1)':
    '@octokit/plugin-request-log': private
  '@octokit/plugin-rest-endpoint-methods@13.3.2-cjs.1(@octokit/core@5.2.1)':
    '@octokit/plugin-rest-endpoint-methods': private
  '@octokit/request-error@5.1.1':
    '@octokit/request-error': private
  '@octokit/request@8.4.1':
    '@octokit/request': private
  '@octokit/rest@20.1.2':
    '@octokit/rest': private
  '@octokit/types@13.10.0':
    '@octokit/types': private
  '@rolldown/pluginutils@1.0.0-beta.11':
    '@rolldown/pluginutils': private
  '@rollup/rollup-android-arm-eabi@4.44.0':
    '@rollup/rollup-android-arm-eabi': private
  '@rollup/rollup-android-arm64@4.44.0':
    '@rollup/rollup-android-arm64': private
  '@rollup/rollup-darwin-arm64@4.44.0':
    '@rollup/rollup-darwin-arm64': private
  '@rollup/rollup-darwin-x64@4.44.0':
    '@rollup/rollup-darwin-x64': private
  '@rollup/rollup-freebsd-arm64@4.44.0':
    '@rollup/rollup-freebsd-arm64': private
  '@rollup/rollup-freebsd-x64@4.44.0':
    '@rollup/rollup-freebsd-x64': private
  '@rollup/rollup-linux-arm-gnueabihf@4.44.0':
    '@rollup/rollup-linux-arm-gnueabihf': private
  '@rollup/rollup-linux-arm-musleabihf@4.44.0':
    '@rollup/rollup-linux-arm-musleabihf': private
  '@rollup/rollup-linux-arm64-gnu@4.44.0':
    '@rollup/rollup-linux-arm64-gnu': private
  '@rollup/rollup-linux-arm64-musl@4.44.0':
    '@rollup/rollup-linux-arm64-musl': private
  '@rollup/rollup-linux-loongarch64-gnu@4.44.0':
    '@rollup/rollup-linux-loongarch64-gnu': private
  '@rollup/rollup-linux-powerpc64le-gnu@4.44.0':
    '@rollup/rollup-linux-powerpc64le-gnu': private
  '@rollup/rollup-linux-riscv64-gnu@4.44.0':
    '@rollup/rollup-linux-riscv64-gnu': private
  '@rollup/rollup-linux-riscv64-musl@4.44.0':
    '@rollup/rollup-linux-riscv64-musl': private
  '@rollup/rollup-linux-s390x-gnu@4.44.0':
    '@rollup/rollup-linux-s390x-gnu': private
  '@rollup/rollup-linux-x64-gnu@4.44.0':
    '@rollup/rollup-linux-x64-gnu': private
  '@rollup/rollup-linux-x64-musl@4.44.0':
    '@rollup/rollup-linux-x64-musl': private
  '@rollup/rollup-win32-arm64-msvc@4.44.0':
    '@rollup/rollup-win32-arm64-msvc': private
  '@rollup/rollup-win32-ia32-msvc@4.44.0':
    '@rollup/rollup-win32-ia32-msvc': private
  '@rollup/rollup-win32-x64-msvc@4.44.0':
    '@rollup/rollup-win32-x64-msvc': private
  '@tsconfig/node10@1.0.11':
    '@tsconfig/node10': private
  '@tsconfig/node12@1.0.11':
    '@tsconfig/node12': private
  '@tsconfig/node14@1.0.3':
    '@tsconfig/node14': private
  '@tsconfig/node16@1.0.4':
    '@tsconfig/node16': private
  '@types/babel__core@7.20.5':
    '@types/babel__core': private
  '@types/babel__generator@7.27.0':
    '@types/babel__generator': private
  '@types/babel__template@7.4.4':
    '@types/babel__template': private
  '@types/babel__traverse@7.20.7':
    '@types/babel__traverse': private
  '@types/body-parser@1.19.6':
    '@types/body-parser': private
  '@types/busboy@1.5.4':
    '@types/busboy': private
  '@types/connect@3.4.38':
    '@types/connect': private
  '@types/cors@2.8.19':
    '@types/cors': private
  '@types/estree@1.0.8':
    '@types/estree': private
  '@types/express-fileupload@1.5.1':
    '@types/express-fileupload': private
  '@types/express-serve-static-core@4.19.6':
    '@types/express-serve-static-core': private
  '@types/express@4.17.23':
    '@types/express': private
  '@types/http-errors@2.0.5':
    '@types/http-errors': private
  '@types/json-schema@7.0.15':
    '@types/json-schema': private
  '@types/mime@1.3.5':
    '@types/mime': private
  '@types/node@24.0.3':
    '@types/node': private
  '@types/qs@6.14.0':
    '@types/qs': private
  '@types/range-parser@1.2.7':
    '@types/range-parser': private
  '@types/react-dom@19.1.6(@types/react@19.1.8)':
    '@types/react-dom': private
  '@types/react@19.1.8':
    '@types/react': private
  '@types/send@0.17.5':
    '@types/send': private
  '@types/serve-static@1.15.8':
    '@types/serve-static': private
  '@typescript-eslint/eslint-plugin@8.34.1(@typescript-eslint/parser@8.34.1(eslint@9.29.0)(typescript@5.8.3))(eslint@9.29.0)(typescript@5.8.3)':
    '@typescript-eslint/eslint-plugin': private
  '@typescript-eslint/parser@8.34.1(eslint@9.29.0)(typescript@5.8.3)':
    '@typescript-eslint/parser': private
  '@typescript-eslint/project-service@8.34.1(typescript@5.8.3)':
    '@typescript-eslint/project-service': private
  '@typescript-eslint/scope-manager@8.34.1':
    '@typescript-eslint/scope-manager': private
  '@typescript-eslint/tsconfig-utils@8.34.1(typescript@5.8.3)':
    '@typescript-eslint/tsconfig-utils': private
  '@typescript-eslint/type-utils@8.34.1(eslint@9.29.0)(typescript@5.8.3)':
    '@typescript-eslint/type-utils': private
  '@typescript-eslint/types@8.34.1':
    '@typescript-eslint/types': private
  '@typescript-eslint/typescript-estree@8.34.1(typescript@5.8.3)':
    '@typescript-eslint/typescript-estree': private
  '@typescript-eslint/utils@8.34.1(eslint@9.29.0)(typescript@5.8.3)':
    '@typescript-eslint/utils': private
  '@typescript-eslint/visitor-keys@8.34.1':
    '@typescript-eslint/visitor-keys': private
  '@vitejs/plugin-react@4.5.2(vite@6.3.5(@types/node@24.0.3)(tsx@4.20.3))':
    '@vitejs/plugin-react': private
  accepts@1.3.8:
    accepts: private
  acorn-jsx@5.3.2(acorn@8.15.0):
    acorn-jsx: private
  acorn-walk@8.3.4:
    acorn-walk: private
  acorn@8.15.0:
    acorn: private
  adm-zip@0.5.16:
    adm-zip: private
  ajv@6.12.6:
    ajv: private
  ansi-regex@5.0.1:
    ansi-regex: private
  ansi-styles@4.3.0:
    ansi-styles: private
  anymatch@3.1.3:
    anymatch: private
  apps/api:
    api: private
  apps/dashboard:
    dashboard: private
  apps/worker:
    worker: private
  arg@4.1.3:
    arg: private
  argparse@2.0.1:
    argparse: private
  array-flatten@1.1.1:
    array-flatten: private
  balanced-match@1.0.2:
    balanced-match: private
  before-after-hook@2.2.3:
    before-after-hook: private
  binary-extensions@2.3.0:
    binary-extensions: private
  body-parser@1.20.3:
    body-parser: private
  brace-expansion@1.1.12:
    brace-expansion: private
  braces@3.0.3:
    braces: private
  browserslist@4.25.0:
    browserslist: private
  bullmq@4.18.2:
    bullmq: private
  busboy@1.6.0:
    busboy: private
  bytes@3.1.2:
    bytes: private
  call-bind-apply-helpers@1.0.2:
    call-bind-apply-helpers: private
  call-bound@1.0.4:
    call-bound: private
  callsites@3.1.0:
    callsites: private
  caniuse-lite@1.0.30001724:
    caniuse-lite: private
  chalk@4.1.2:
    chalk: private
  chokidar@3.6.0:
    chokidar: private
  cliui@8.0.1:
    cliui: private
  cluster-key-slot@1.1.2:
    cluster-key-slot: private
  color-convert@2.0.1:
    color-convert: private
  color-name@1.1.4:
    color-name: private
  concat-map@0.0.1:
    concat-map: private
  content-disposition@0.5.4:
    content-disposition: private
  content-type@1.0.5:
    content-type: private
  convert-source-map@2.0.0:
    convert-source-map: private
  cookie-signature@1.0.6:
    cookie-signature: private
  cookie@0.7.1:
    cookie: private
  cors@2.8.5:
    cors: private
  create-require@1.1.1:
    create-require: private
  cron-parser@4.9.0:
    cron-parser: private
  cross-spawn@7.0.6:
    cross-spawn: private
  csstype@3.1.3:
    csstype: private
  date-fns@2.30.0:
    date-fns: private
  debug@4.4.1(supports-color@5.5.0):
    debug: private
  deep-is@0.1.4:
    deep-is: private
  denque@2.1.0:
    denque: private
  depd@2.0.0:
    depd: private
  deprecation@2.3.1:
    deprecation: private
  destroy@1.2.0:
    destroy: private
  detect-libc@2.0.4:
    detect-libc: private
  diff@4.0.2:
    diff: private
  dotenv@16.5.0:
    dotenv: private
  dunder-proto@1.0.1:
    dunder-proto: private
  ee-first@1.1.1:
    ee-first: private
  electron-to-chromium@1.5.171:
    electron-to-chromium: private
  emoji-regex@8.0.0:
    emoji-regex: private
  encodeurl@2.0.0:
    encodeurl: private
  es-define-property@1.0.1:
    es-define-property: private
  es-errors@1.3.0:
    es-errors: private
  es-object-atoms@1.1.1:
    es-object-atoms: private
  esbuild@0.25.5:
    esbuild: private
  escalade@3.2.0:
    escalade: private
  escape-html@1.0.3:
    escape-html: private
  escape-string-regexp@4.0.0:
    escape-string-regexp: private
  eslint-plugin-react-hooks@5.2.0(eslint@9.29.0):
    eslint-plugin-react-hooks: private
  eslint-plugin-react-refresh@0.4.20(eslint@9.29.0):
    eslint-plugin-react-refresh: private
  eslint-scope@8.4.0:
    eslint-scope: private
  eslint-visitor-keys@4.2.1:
    eslint-visitor-keys: private
  eslint@9.29.0:
    eslint: private
  espree@10.4.0:
    espree: private
  esquery@1.6.0:
    esquery: private
  esrecurse@4.3.0:
    esrecurse: private
  estraverse@5.3.0:
    estraverse: private
  esutils@2.0.3:
    esutils: private
  etag@1.8.1:
    etag: private
  execa@8.0.1:
    execa: private
  express-fileupload@1.5.1:
    express-fileupload: private
  express@4.21.2:
    express: private
  fast-deep-equal@3.1.3:
    fast-deep-equal: private
  fast-glob@3.3.3:
    fast-glob: private
  fast-json-stable-stringify@2.1.0:
    fast-json-stable-stringify: private
  fast-levenshtein@2.0.6:
    fast-levenshtein: private
  fastq@1.19.1:
    fastq: private
  fdir@6.4.6(picomatch@4.0.2):
    fdir: private
  file-entry-cache@8.0.0:
    file-entry-cache: private
  fill-range@7.1.1:
    fill-range: private
  finalhandler@1.3.1:
    finalhandler: private
  find-up@5.0.0:
    find-up: private
  flat-cache@4.0.1:
    flat-cache: private
  flatted@3.3.3:
    flatted: private
  forwarded@0.2.0:
    forwarded: private
  fresh@0.5.2:
    fresh: private
  fs.realpath@1.0.0:
    fs.realpath: private
  fsevents@2.3.3:
    fsevents: private
  function-bind@1.1.2:
    function-bind: private
  gensync@1.0.0-beta.2:
    gensync: private
  get-caller-file@2.0.5:
    get-caller-file: private
  get-intrinsic@1.3.0:
    get-intrinsic: private
  get-proto@1.0.1:
    get-proto: private
  get-stream@8.0.1:
    get-stream: private
  get-tsconfig@4.10.1:
    get-tsconfig: private
  glob-parent@6.0.2:
    glob-parent: private
  glob@8.1.0:
    glob: private
  globals@16.2.0:
    globals: private
  gopd@1.2.0:
    gopd: private
  graphemer@1.4.0:
    graphemer: private
  has-flag@3.0.0:
    has-flag: private
  has-symbols@1.1.0:
    has-symbols: private
  hasown@2.0.2:
    hasown: private
  http-errors@2.0.0:
    http-errors: private
  human-signals@5.0.0:
    human-signals: private
  iconv-lite@0.4.24:
    iconv-lite: private
  ignore-by-default@1.0.1:
    ignore-by-default: private
  ignore@5.3.2:
    ignore: private
  import-fresh@3.3.1:
    import-fresh: private
  imurmurhash@0.1.4:
    imurmurhash: private
  inflight@1.0.6:
    inflight: private
  inherits@2.0.4:
    inherits: private
  ioredis@5.6.1:
    ioredis: private
  ipaddr.js@1.9.1:
    ipaddr.js: private
  is-binary-path@2.1.0:
    is-binary-path: private
  is-extglob@2.1.1:
    is-extglob: private
  is-fullwidth-code-point@3.0.0:
    is-fullwidth-code-point: private
  is-glob@4.0.3:
    is-glob: private
  is-number@7.0.0:
    is-number: private
  is-stream@3.0.0:
    is-stream: private
  isexe@2.0.0:
    isexe: private
  js-tokens@4.0.0:
    js-tokens: private
  js-yaml@4.1.0:
    js-yaml: private
  jsesc@3.1.0:
    jsesc: private
  json-buffer@3.0.1:
    json-buffer: private
  json-schema-traverse@0.4.1:
    json-schema-traverse: private
  json-stable-stringify-without-jsonify@1.0.1:
    json-stable-stringify-without-jsonify: private
  json5@2.2.3:
    json5: private
  keyv@4.5.4:
    keyv: private
  levn@0.4.1:
    levn: private
  locate-path@6.0.0:
    locate-path: private
  lodash.defaults@4.2.0:
    lodash.defaults: private
  lodash.isarguments@3.1.0:
    lodash.isarguments: private
  lodash.merge@4.6.2:
    lodash.merge: private
  lodash@4.17.21:
    lodash: private
  lru-cache@5.1.1:
    lru-cache: private
  luxon@3.6.1:
    luxon: private
  make-error@1.3.6:
    make-error: private
  math-intrinsics@1.1.0:
    math-intrinsics: private
  media-typer@0.3.0:
    media-typer: private
  merge-descriptors@1.0.3:
    merge-descriptors: private
  merge-stream@2.0.0:
    merge-stream: private
  merge2@1.4.1:
    merge2: private
  methods@1.1.2:
    methods: private
  micromatch@4.0.8:
    micromatch: private
  mime-db@1.52.0:
    mime-db: private
  mime-types@2.1.35:
    mime-types: private
  mime@1.6.0:
    mime: private
  mimic-fn@4.0.0:
    mimic-fn: private
  minimatch@3.1.2:
    minimatch: private
  ms@2.0.0:
    ms: private
  msgpackr-extract@3.0.3:
    msgpackr-extract: private
  msgpackr@1.11.4:
    msgpackr: private
  nanoid@3.3.11:
    nanoid: private
  natural-compare@1.4.0:
    natural-compare: private
  negotiator@0.6.3:
    negotiator: private
  node-abort-controller@3.1.1:
    node-abort-controller: private
  node-gyp-build-optional-packages@5.2.2:
    node-gyp-build-optional-packages: private
  node-releases@2.0.19:
    node-releases: private
  nodemon@3.1.10:
    nodemon: private
  normalize-path@3.0.0:
    normalize-path: private
  npm-run-path@5.3.0:
    npm-run-path: private
  object-assign@4.1.1:
    object-assign: private
  object-inspect@1.13.4:
    object-inspect: private
  on-finished@2.4.1:
    on-finished: private
  once@1.4.0:
    once: private
  onetime@6.0.0:
    onetime: private
  optionator@0.9.4:
    optionator: private
  p-limit@3.1.0:
    p-limit: private
  p-locate@5.0.0:
    p-locate: private
  parent-module@1.0.1:
    parent-module: private
  parseurl@1.3.3:
    parseurl: private
  path-exists@4.0.0:
    path-exists: private
  path-key@3.1.1:
    path-key: private
  path-to-regexp@0.1.12:
    path-to-regexp: private
  picocolors@1.1.1:
    picocolors: private
  picomatch@4.0.2:
    picomatch: private
  postcss@8.5.6:
    postcss: private
  prelude-ls@1.2.1:
    prelude-ls: private
  proxy-addr@2.0.7:
    proxy-addr: private
  pstree.remy@1.1.8:
    pstree.remy: private
  punycode@2.3.1:
    punycode: private
  qs@6.13.0:
    qs: private
  queue-microtask@1.2.3:
    queue-microtask: private
  range-parser@1.2.1:
    range-parser: private
  raw-body@2.5.2:
    raw-body: private
  react-dom@19.1.0(react@19.1.0):
    react-dom: private
  react-refresh@0.17.0:
    react-refresh: private
  react@19.1.0:
    react: private
  readdirp@3.6.0:
    readdirp: private
  redis-errors@1.2.0:
    redis-errors: private
  redis-parser@3.0.0:
    redis-parser: private
  require-directory@2.1.1:
    require-directory: private
  resolve-from@4.0.0:
    resolve-from: private
  resolve-pkg-maps@1.0.0:
    resolve-pkg-maps: private
  reusify@1.1.0:
    reusify: private
  rollup@4.44.0:
    rollup: private
  run-parallel@1.2.0:
    run-parallel: private
  rxjs@7.8.2:
    rxjs: private
  safe-buffer@5.2.1:
    safe-buffer: private
  safer-buffer@2.1.2:
    safer-buffer: private
  scheduler@0.26.0:
    scheduler: private
  semver@7.7.2:
    semver: private
  send@0.19.0:
    send: private
  serve-static@1.16.2:
    serve-static: private
  setprototypeof@1.2.0:
    setprototypeof: private
  shebang-command@2.0.0:
    shebang-command: private
  shebang-regex@3.0.0:
    shebang-regex: private
  shell-quote@1.8.3:
    shell-quote: private
  side-channel-list@1.0.0:
    side-channel-list: private
  side-channel-map@1.0.1:
    side-channel-map: private
  side-channel-weakmap@1.0.2:
    side-channel-weakmap: private
  side-channel@1.1.0:
    side-channel: private
  signal-exit@4.1.0:
    signal-exit: private
  simple-update-notifier@2.0.0:
    simple-update-notifier: private
  source-map-js@1.2.1:
    source-map-js: private
  spawn-command@0.0.2:
    spawn-command: private
  standard-as-callback@2.1.0:
    standard-as-callback: private
  statuses@2.0.1:
    statuses: private
  streamsearch@1.1.0:
    streamsearch: private
  string-width@4.2.3:
    string-width: private
  strip-ansi@6.0.1:
    strip-ansi: private
  strip-final-newline@3.0.0:
    strip-final-newline: private
  strip-json-comments@3.1.1:
    strip-json-comments: private
  supports-color@8.1.1:
    supports-color: private
  tinyglobby@0.2.14:
    tinyglobby: private
  tmp-promise@3.0.3:
    tmp-promise: private
  tmp@0.2.3:
    tmp: private
  to-regex-range@5.0.1:
    to-regex-range: private
  toidentifier@1.0.1:
    toidentifier: private
  touch@3.1.1:
    touch: private
  tree-kill@1.2.2:
    tree-kill: private
  ts-api-utils@2.1.0(typescript@5.8.3):
    ts-api-utils: private
  ts-node@10.9.2(@types/node@24.0.3)(typescript@5.8.3):
    ts-node: private
  tslib@2.8.1:
    tslib: private
  tsx@4.20.3:
    tsx: private
  type-check@0.4.0:
    type-check: private
  type-is@1.6.18:
    type-is: private
  typescript-eslint@8.34.1(eslint@9.29.0)(typescript@5.8.3):
    typescript-eslint: private
  typescript@5.8.3:
    typescript: private
  undefsafe@2.0.5:
    undefsafe: private
  undici-types@7.8.0:
    undici-types: private
  universal-user-agent@6.0.1:
    universal-user-agent: private
  unpipe@1.0.0:
    unpipe: private
  update-browserslist-db@1.1.3(browserslist@4.25.0):
    update-browserslist-db: private
  uri-js@4.4.1:
    uri-js: private
  utils-merge@1.0.1:
    utils-merge: private
  uuid@9.0.1:
    uuid: private
  v8-compile-cache-lib@3.0.1:
    v8-compile-cache-lib: private
  vary@1.1.2:
    vary: private
  vite@6.3.5(@types/node@24.0.3)(tsx@4.20.3):
    vite: private
  which@2.0.2:
    which: private
  word-wrap@1.2.5:
    word-wrap: private
  wrap-ansi@7.0.0:
    wrap-ansi: private
  wrappy@1.0.2:
    wrappy: private
  y18n@5.0.8:
    y18n: private
  yallist@3.1.1:
    yallist: private
  yargs-parser@21.1.1:
    yargs-parser: private
  yargs@17.7.2:
    yargs: private
  yn@3.1.1:
    yn: private
  yocto-queue@0.1.0:
    yocto-queue: private
ignoredBuilds:
  - esbuild
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.12.1
pendingBuilds: []
prunedAt: Fri, 20 Jun 2025 14:29:22 GMT
publicHoistPattern: []
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmjs.org/
skipped:
  - '@esbuild/aix-ppc64@0.25.5'
  - '@esbuild/android-arm64@0.25.5'
  - '@esbuild/android-arm@0.25.5'
  - '@esbuild/android-x64@0.25.5'
  - '@esbuild/darwin-arm64@0.25.5'
  - '@esbuild/darwin-x64@0.25.5'
  - '@esbuild/freebsd-arm64@0.25.5'
  - '@esbuild/freebsd-x64@0.25.5'
  - '@esbuild/linux-arm64@0.25.5'
  - '@esbuild/linux-arm@0.25.5'
  - '@esbuild/linux-ia32@0.25.5'
  - '@esbuild/linux-loong64@0.25.5'
  - '@esbuild/linux-mips64el@0.25.5'
  - '@esbuild/linux-ppc64@0.25.5'
  - '@esbuild/linux-riscv64@0.25.5'
  - '@esbuild/linux-s390x@0.25.5'
  - '@esbuild/linux-x64@0.25.5'
  - '@esbuild/netbsd-arm64@0.25.5'
  - '@esbuild/netbsd-x64@0.25.5'
  - '@esbuild/openbsd-arm64@0.25.5'
  - '@esbuild/openbsd-x64@0.25.5'
  - '@esbuild/sunos-x64@0.25.5'
  - '@esbuild/win32-arm64@0.25.5'
  - '@esbuild/win32-ia32@0.25.5'
  - '@msgpackr-extract/msgpackr-extract-darwin-arm64@3.0.3'
  - '@msgpackr-extract/msgpackr-extract-darwin-x64@3.0.3'
  - '@msgpackr-extract/msgpackr-extract-linux-arm64@3.0.3'
  - '@msgpackr-extract/msgpackr-extract-linux-arm@3.0.3'
  - '@msgpackr-extract/msgpackr-extract-linux-x64@3.0.3'
  - '@rollup/rollup-android-arm-eabi@4.44.0'
  - '@rollup/rollup-android-arm64@4.44.0'
  - '@rollup/rollup-darwin-arm64@4.44.0'
  - '@rollup/rollup-darwin-x64@4.44.0'
  - '@rollup/rollup-freebsd-arm64@4.44.0'
  - '@rollup/rollup-freebsd-x64@4.44.0'
  - '@rollup/rollup-linux-arm-gnueabihf@4.44.0'
  - '@rollup/rollup-linux-arm-musleabihf@4.44.0'
  - '@rollup/rollup-linux-arm64-gnu@4.44.0'
  - '@rollup/rollup-linux-arm64-musl@4.44.0'
  - '@rollup/rollup-linux-loongarch64-gnu@4.44.0'
  - '@rollup/rollup-linux-powerpc64le-gnu@4.44.0'
  - '@rollup/rollup-linux-riscv64-gnu@4.44.0'
  - '@rollup/rollup-linux-riscv64-musl@4.44.0'
  - '@rollup/rollup-linux-s390x-gnu@4.44.0'
  - '@rollup/rollup-linux-x64-gnu@4.44.0'
  - '@rollup/rollup-linux-x64-musl@4.44.0'
  - '@rollup/rollup-win32-arm64-msvc@4.44.0'
  - '@rollup/rollup-win32-ia32-msvc@4.44.0'
  - fsevents@2.3.3
storeDir: C:\Users\<USER>\AppData\Local\pnpm\store\v10
virtualStoreDir: C:\Users\<USER>\Desktop\websites\New folder\public-link\node_modules\.pnpm
virtualStoreDirMaxLength: 60
