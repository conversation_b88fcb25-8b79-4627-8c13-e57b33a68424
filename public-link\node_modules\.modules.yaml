hoistPattern:
  - '*'
hoistedDependencies:
  '@babel/runtime@7.27.6':
    '@babel/runtime': private
  '@cspotcode/source-map-support@0.8.1':
    '@cspotcode/source-map-support': private
  '@esbuild/aix-ppc64@0.25.5':
    '@esbuild/aix-ppc64': private
  '@esbuild/android-arm64@0.25.5':
    '@esbuild/android-arm64': private
  '@esbuild/android-arm@0.25.5':
    '@esbuild/android-arm': private
  '@esbuild/android-x64@0.25.5':
    '@esbuild/android-x64': private
  '@esbuild/darwin-arm64@0.25.5':
    '@esbuild/darwin-arm64': private
  '@esbuild/darwin-x64@0.25.5':
    '@esbuild/darwin-x64': private
  '@esbuild/freebsd-arm64@0.25.5':
    '@esbuild/freebsd-arm64': private
  '@esbuild/freebsd-x64@0.25.5':
    '@esbuild/freebsd-x64': private
  '@esbuild/linux-arm64@0.25.5':
    '@esbuild/linux-arm64': private
  '@esbuild/linux-arm@0.25.5':
    '@esbuild/linux-arm': private
  '@esbuild/linux-ia32@0.25.5':
    '@esbuild/linux-ia32': private
  '@esbuild/linux-loong64@0.25.5':
    '@esbuild/linux-loong64': private
  '@esbuild/linux-mips64el@0.25.5':
    '@esbuild/linux-mips64el': private
  '@esbuild/linux-ppc64@0.25.5':
    '@esbuild/linux-ppc64': private
  '@esbuild/linux-riscv64@0.25.5':
    '@esbuild/linux-riscv64': private
  '@esbuild/linux-s390x@0.25.5':
    '@esbuild/linux-s390x': private
  '@esbuild/linux-x64@0.25.5':
    '@esbuild/linux-x64': private
  '@esbuild/netbsd-arm64@0.25.5':
    '@esbuild/netbsd-arm64': private
  '@esbuild/netbsd-x64@0.25.5':
    '@esbuild/netbsd-x64': private
  '@esbuild/openbsd-arm64@0.25.5':
    '@esbuild/openbsd-arm64': private
  '@esbuild/openbsd-x64@0.25.5':
    '@esbuild/openbsd-x64': private
  '@esbuild/sunos-x64@0.25.5':
    '@esbuild/sunos-x64': private
  '@esbuild/win32-arm64@0.25.5':
    '@esbuild/win32-arm64': private
  '@esbuild/win32-ia32@0.25.5':
    '@esbuild/win32-ia32': private
  '@esbuild/win32-x64@0.25.5':
    '@esbuild/win32-x64': private
  '@ioredis/commands@1.2.0':
    '@ioredis/commands': private
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': private
  '@jridgewell/sourcemap-codec@1.5.0':
    '@jridgewell/sourcemap-codec': private
  '@jridgewell/trace-mapping@0.3.9':
    '@jridgewell/trace-mapping': private
  '@msgpackr-extract/msgpackr-extract-darwin-arm64@3.0.3':
    '@msgpackr-extract/msgpackr-extract-darwin-arm64': private
  '@msgpackr-extract/msgpackr-extract-darwin-x64@3.0.3':
    '@msgpackr-extract/msgpackr-extract-darwin-x64': private
  '@msgpackr-extract/msgpackr-extract-linux-arm64@3.0.3':
    '@msgpackr-extract/msgpackr-extract-linux-arm64': private
  '@msgpackr-extract/msgpackr-extract-linux-arm@3.0.3':
    '@msgpackr-extract/msgpackr-extract-linux-arm': private
  '@msgpackr-extract/msgpackr-extract-linux-x64@3.0.3':
    '@msgpackr-extract/msgpackr-extract-linux-x64': private
  '@msgpackr-extract/msgpackr-extract-win32-x64@3.0.3':
    '@msgpackr-extract/msgpackr-extract-win32-x64': private
  '@octokit/auth-token@4.0.0':
    '@octokit/auth-token': private
  '@octokit/core@5.2.1':
    '@octokit/core': private
  '@octokit/endpoint@9.0.6':
    '@octokit/endpoint': private
  '@octokit/graphql@7.1.1':
    '@octokit/graphql': private
  '@octokit/openapi-types@24.2.0':
    '@octokit/openapi-types': private
  '@octokit/plugin-paginate-rest@11.4.4-cjs.2(@octokit/core@5.2.1)':
    '@octokit/plugin-paginate-rest': private
  '@octokit/plugin-request-log@4.0.1(@octokit/core@5.2.1)':
    '@octokit/plugin-request-log': private
  '@octokit/plugin-rest-endpoint-methods@13.3.2-cjs.1(@octokit/core@5.2.1)':
    '@octokit/plugin-rest-endpoint-methods': private
  '@octokit/request-error@5.1.1':
    '@octokit/request-error': private
  '@octokit/request@8.4.1':
    '@octokit/request': private
  '@octokit/rest@20.1.2':
    '@octokit/rest': private
  '@octokit/types@13.10.0':
    '@octokit/types': private
  '@tsconfig/node10@1.0.11':
    '@tsconfig/node10': private
  '@tsconfig/node12@1.0.11':
    '@tsconfig/node12': private
  '@tsconfig/node14@1.0.3':
    '@tsconfig/node14': private
  '@tsconfig/node16@1.0.4':
    '@tsconfig/node16': private
  '@types/node@24.0.3':
    '@types/node': private
  acorn-walk@8.3.4:
    acorn-walk: private
  acorn@8.15.0:
    acorn: private
  adm-zip@0.5.16:
    adm-zip: private
  ansi-regex@5.0.1:
    ansi-regex: private
  ansi-styles@4.3.0:
    ansi-styles: private
  anymatch@3.1.3:
    anymatch: private
  apps/api:
    api: private
  apps/dashboard:
    dashboard: private
  apps/worker:
    worker: private
  arg@4.1.3:
    arg: private
  balanced-match@1.0.2:
    balanced-match: private
  before-after-hook@2.2.3:
    before-after-hook: private
  binary-extensions@2.3.0:
    binary-extensions: private
  brace-expansion@1.1.12:
    brace-expansion: private
  braces@3.0.3:
    braces: private
  bullmq@4.18.2:
    bullmq: private
  chalk@4.1.2:
    chalk: private
  chokidar@3.6.0:
    chokidar: private
  cliui@8.0.1:
    cliui: private
  cluster-key-slot@1.1.2:
    cluster-key-slot: private
  color-convert@2.0.1:
    color-convert: private
  color-name@1.1.4:
    color-name: private
  concat-map@0.0.1:
    concat-map: private
  create-require@1.1.1:
    create-require: private
  cron-parser@4.9.0:
    cron-parser: private
  cross-spawn@7.0.6:
    cross-spawn: private
  date-fns@2.30.0:
    date-fns: private
  debug@4.4.1(supports-color@5.5.0):
    debug: private
  denque@2.1.0:
    denque: private
  deprecation@2.3.1:
    deprecation: private
  detect-libc@2.0.4:
    detect-libc: private
  diff@4.0.2:
    diff: private
  dotenv@16.5.0:
    dotenv: private
  emoji-regex@8.0.0:
    emoji-regex: private
  esbuild@0.25.5:
    esbuild: private
  escalade@3.2.0:
    escalade: private
  execa@8.0.1:
    execa: private
  fill-range@7.1.1:
    fill-range: private
  fs.realpath@1.0.0:
    fs.realpath: private
  fsevents@2.3.3:
    fsevents: private
  get-caller-file@2.0.5:
    get-caller-file: private
  get-stream@8.0.1:
    get-stream: private
  get-tsconfig@4.10.1:
    get-tsconfig: private
  glob-parent@5.1.2:
    glob-parent: private
  glob@8.1.0:
    glob: private
  has-flag@3.0.0:
    has-flag: private
  human-signals@5.0.0:
    human-signals: private
  ignore-by-default@1.0.1:
    ignore-by-default: private
  inflight@1.0.6:
    inflight: private
  inherits@2.0.4:
    inherits: private
  ioredis@5.6.1:
    ioredis: private
  is-binary-path@2.1.0:
    is-binary-path: private
  is-extglob@2.1.1:
    is-extglob: private
  is-fullwidth-code-point@3.0.0:
    is-fullwidth-code-point: private
  is-glob@4.0.3:
    is-glob: private
  is-number@7.0.0:
    is-number: private
  is-stream@3.0.0:
    is-stream: private
  isexe@2.0.0:
    isexe: private
  lodash.defaults@4.2.0:
    lodash.defaults: private
  lodash.isarguments@3.1.0:
    lodash.isarguments: private
  lodash@4.17.21:
    lodash: private
  luxon@3.6.1:
    luxon: private
  make-error@1.3.6:
    make-error: private
  merge-stream@2.0.0:
    merge-stream: private
  mimic-fn@4.0.0:
    mimic-fn: private
  minimatch@3.1.2:
    minimatch: private
  ms@2.1.3:
    ms: private
  msgpackr-extract@3.0.3:
    msgpackr-extract: private
  msgpackr@1.11.4:
    msgpackr: private
  node-abort-controller@3.1.1:
    node-abort-controller: private
  node-gyp-build-optional-packages@5.2.2:
    node-gyp-build-optional-packages: private
  nodemon@3.1.10:
    nodemon: private
  normalize-path@3.0.0:
    normalize-path: private
  npm-run-path@5.3.0:
    npm-run-path: private
  once@1.4.0:
    once: private
  onetime@6.0.0:
    onetime: private
  path-key@3.1.1:
    path-key: private
  picomatch@2.3.1:
    picomatch: private
  pstree.remy@1.1.8:
    pstree.remy: private
  readdirp@3.6.0:
    readdirp: private
  redis-errors@1.2.0:
    redis-errors: private
  redis-parser@3.0.0:
    redis-parser: private
  require-directory@2.1.1:
    require-directory: private
  resolve-pkg-maps@1.0.0:
    resolve-pkg-maps: private
  rxjs@7.8.2:
    rxjs: private
  semver@7.7.2:
    semver: private
  shebang-command@2.0.0:
    shebang-command: private
  shebang-regex@3.0.0:
    shebang-regex: private
  shell-quote@1.8.3:
    shell-quote: private
  signal-exit@4.1.0:
    signal-exit: private
  simple-update-notifier@2.0.0:
    simple-update-notifier: private
  spawn-command@0.0.2:
    spawn-command: private
  standard-as-callback@2.1.0:
    standard-as-callback: private
  string-width@4.2.3:
    string-width: private
  strip-ansi@6.0.1:
    strip-ansi: private
  strip-final-newline@3.0.0:
    strip-final-newline: private
  supports-color@8.1.1:
    supports-color: private
  tmp-promise@3.0.3:
    tmp-promise: private
  tmp@0.2.3:
    tmp: private
  to-regex-range@5.0.1:
    to-regex-range: private
  touch@3.1.1:
    touch: private
  tree-kill@1.2.2:
    tree-kill: private
  ts-node@10.9.2(@types/node@24.0.3)(typescript@5.8.3):
    ts-node: private
  tslib@2.8.1:
    tslib: private
  tsx@4.20.3:
    tsx: private
  typescript@5.8.3:
    typescript: private
  undefsafe@2.0.5:
    undefsafe: private
  undici-types@7.8.0:
    undici-types: private
  universal-user-agent@6.0.1:
    universal-user-agent: private
  uuid@9.0.1:
    uuid: private
  v8-compile-cache-lib@3.0.1:
    v8-compile-cache-lib: private
  which@2.0.2:
    which: private
  wrap-ansi@7.0.0:
    wrap-ansi: private
  wrappy@1.0.2:
    wrappy: private
  y18n@5.0.8:
    y18n: private
  yargs-parser@21.1.1:
    yargs-parser: private
  yargs@17.7.2:
    yargs: private
  yn@3.1.1:
    yn: private
ignoredBuilds:
  - esbuild
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.12.1
pendingBuilds: []
prunedAt: Fri, 20 Jun 2025 14:29:22 GMT
publicHoistPattern: []
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmjs.org/
skipped:
  - '@esbuild/aix-ppc64@0.25.5'
  - '@esbuild/android-arm64@0.25.5'
  - '@esbuild/android-arm@0.25.5'
  - '@esbuild/android-x64@0.25.5'
  - '@esbuild/darwin-arm64@0.25.5'
  - '@esbuild/darwin-x64@0.25.5'
  - '@esbuild/freebsd-arm64@0.25.5'
  - '@esbuild/freebsd-x64@0.25.5'
  - '@esbuild/linux-arm64@0.25.5'
  - '@esbuild/linux-arm@0.25.5'
  - '@esbuild/linux-ia32@0.25.5'
  - '@esbuild/linux-loong64@0.25.5'
  - '@esbuild/linux-mips64el@0.25.5'
  - '@esbuild/linux-ppc64@0.25.5'
  - '@esbuild/linux-riscv64@0.25.5'
  - '@esbuild/linux-s390x@0.25.5'
  - '@esbuild/linux-x64@0.25.5'
  - '@esbuild/netbsd-arm64@0.25.5'
  - '@esbuild/netbsd-x64@0.25.5'
  - '@esbuild/openbsd-arm64@0.25.5'
  - '@esbuild/openbsd-x64@0.25.5'
  - '@esbuild/sunos-x64@0.25.5'
  - '@esbuild/win32-arm64@0.25.5'
  - '@esbuild/win32-ia32@0.25.5'
  - '@msgpackr-extract/msgpackr-extract-darwin-arm64@3.0.3'
  - '@msgpackr-extract/msgpackr-extract-darwin-x64@3.0.3'
  - '@msgpackr-extract/msgpackr-extract-linux-arm64@3.0.3'
  - '@msgpackr-extract/msgpackr-extract-linux-arm@3.0.3'
  - '@msgpackr-extract/msgpackr-extract-linux-x64@3.0.3'
  - '@rollup/rollup-android-arm-eabi@4.44.0'
  - '@rollup/rollup-android-arm64@4.44.0'
  - '@rollup/rollup-darwin-arm64@4.44.0'
  - '@rollup/rollup-darwin-x64@4.44.0'
  - '@rollup/rollup-freebsd-arm64@4.44.0'
  - '@rollup/rollup-freebsd-x64@4.44.0'
  - '@rollup/rollup-linux-arm-gnueabihf@4.44.0'
  - '@rollup/rollup-linux-arm-musleabihf@4.44.0'
  - '@rollup/rollup-linux-arm64-gnu@4.44.0'
  - '@rollup/rollup-linux-arm64-musl@4.44.0'
  - '@rollup/rollup-linux-loongarch64-gnu@4.44.0'
  - '@rollup/rollup-linux-powerpc64le-gnu@4.44.0'
  - '@rollup/rollup-linux-riscv64-gnu@4.44.0'
  - '@rollup/rollup-linux-riscv64-musl@4.44.0'
  - '@rollup/rollup-linux-s390x-gnu@4.44.0'
  - '@rollup/rollup-linux-x64-gnu@4.44.0'
  - '@rollup/rollup-linux-x64-musl@4.44.0'
  - '@rollup/rollup-win32-arm64-msvc@4.44.0'
  - '@rollup/rollup-win32-ia32-msvc@4.44.0'
  - fsevents@2.3.3
storeDir: C:\Users\<USER>\AppData\Local\pnpm\store\v10
virtualStoreDir: C:\Users\<USER>\Desktop\websites\New folder\public-link\node_modules\.pnpm
virtualStoreDirMaxLength: 60
