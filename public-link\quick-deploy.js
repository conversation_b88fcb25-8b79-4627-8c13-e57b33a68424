import { Octokit } from '@octokit/rest';

const GITHUB_TOKEN = process.env.GITHUB_TOKEN || 'your-github-token-here';
const octokit = new Octokit({ auth: GITHUB_TOKEN });

async function deployTestPage() {
  try {
    const html = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Public-Link Test Page</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
            padding: 50px;
            margin: 0;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
        h1 { font-size: 3em; margin-bottom: 20px; }
        p { font-size: 1.2em; margin: 10px 0; }
        .success { background: rgba(255,255,255,0.1); padding: 20px; border-radius: 10px; margin: 20px 0; }
    </style>
</head>
<body>
    <h1>🎉 Public-Link Success!</h1>
    <div class="success">
        <p>✅ This page was successfully deployed using Public-Link!</p>
        <p>🚀 Your localhost project is now publicly accessible</p>
        <p>🌐 Share this URL with anyone around the world</p>
    </div>
    <p>Generated at: ${new Date().toLocaleString()}</p>
</body>
</html>`;
    
    const repoName = `public-link-test-${Date.now()}`;
    
    console.log(`Creating test deployment: ${repoName}`);
    const repo = await octokit.repos.createForAuthenticatedUser({
      name: repoName,
      description: 'Test deployment from Public-Link system',
      public: true,
      auto_init: false
    });
    
    await octokit.repos.createOrUpdateFileContents({
      owner: repo.data.owner.login,
      repo: repo.data.name,
      path: 'index.html',
      message: 'Deploy test page via Public-Link',
      content: Buffer.from(html).toString('base64')
    });
    
    await octokit.repos.createPagesSite({
      owner: repo.data.owner.login,
      repo: repo.data.name,
      source: { branch: 'main', path: '/' }
    });
    
    const publicUrl = `https://${repo.data.owner.login}.github.io/${repo.data.name}`;
    
    console.log('\n🎉 SUCCESS! Test page deployed!');
    console.log(`🔗 Public URL: ${publicUrl}`);
    console.log('⏰ Will be live in 2-3 minutes');
    
    return publicUrl;
    
  } catch (error) {
    console.error('❌ Deployment failed:', error.message);
  }
}

deployTestPage();
