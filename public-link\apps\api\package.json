{"name": "api", "version": "0.1.0", "main": "dist/index.js", "type": "module", "scripts": {"dev": "nodemon --exec tsx src/index.ts", "start": "tsx src/index.ts", "build": "tsc"}, "dependencies": {"@octokit/rest": "^20.0.0", "bullmq": "^4.0.3", "cors": "^2.8.5", "dotenv": "^16.4.0", "express": "^4.19.0", "express-fileupload": "^1.4.1", "ioredis": "^5.3.2"}, "devDependencies": {"@types/cors": "^2.8.19", "@types/express": "^4.17.21", "@types/express-fileupload": "^1.4.4", "nodemon": "^3.0.3", "ts-node": "^10.9.2", "tsx": "^4.20.3", "typescript": "^5.5.0"}}