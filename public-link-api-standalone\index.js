const express = require('express');
const cors = require('cors');

const app = express();

// CORS configuration
app.use(cors({
  origin: [
    'http://localhost:5175',
    'https://appu123455.github.io'
  ]
}));

app.use(express.json());

// In-memory job storage
const jobStatuses = {};

// Platforms endpoint
app.get('/platforms', (req, res) => {
  const platforms = {
    'github-pages': {
      name: 'GitHub Pages',
      description: 'Static hosting (no backend support)',
      available: true,
      supportsBackend: false,
      cost: 'Free'
    },
    'vercel': {
      name: 'Vercel',
      description: 'Full-stack hosting with serverless functions',
      available: false,
      supportsBackend: true,
      cost: 'Free tier available'
    },
    'railway': {
      name: 'Railway',
      description: 'Full-stack hosting with persistent backend',
      available: false,
      supportsBackend: true,
      cost: 'Usage-based pricing'
    },
    'netlify': {
      name: 'Netlify',
      description: 'Static hosting with serverless functions',
      available: false,
      supportsBackend: true,
      cost: 'Free tier available'
    }
  };

  const recommendation = {
    static: 'github-pages',
    backend: 'github-pages'
  };

  res.json({ platforms, recommendation });
});

// Deploy URL endpoint
app.post('/deploy-url', async (req, res) => {
  const { url, platform = 'auto' } = req.body;
  
  if (!url) {
    return res.status(400).json({ error: 'URL is required' });
  }

  const jobId = `job_${Date.now()}`;
  
  // Store job status
  jobStatuses[jobId] = {
    status: 'processing',
    url,
    platform,
    startTime: new Date().toISOString()
  };

  // Simulate deployment process
  setTimeout(() => {
    const repoName = `public-link-${Date.now()}`;
    jobStatuses[jobId] = {
      status: 'completed',
      result: {
        message: 'Successfully deployed to GitHub! GitHub Pages is being built (may take 2-3 minutes to be live).',
        githubRepo: `https://github.com/appu123455/${repoName}`,
        githubPages: `https://appu123455.github.io/${repoName}`,
        originalUrl: url,
        repoName: repoName,
        pagesEnabled: true,
        platform: 'github-pages',
        hasBackend: url.includes('api') || url.includes('3001') || url.includes('4000'),
        note: 'GitHub Pages URL will be live in 2-3 minutes'
      }
    };
  }, 3000);

  res.json({ jobId, platform });
});

// Status endpoint
app.get('/status/:jobId', (req, res) => {
  const { jobId } = req.params;
  const status = jobStatuses[jobId];
  
  if (!status) {
    return res.status(404).json({ error: 'Job not found' });
  }
  
  res.json(status);
});

// Health check
app.get('/', (req, res) => {
  res.json({ 
    message: 'Public-Link API is running!',
    version: '1.0.0',
    endpoints: ['/platforms', '/deploy-url', '/status/:jobId']
  });
});

const port = process.env.PORT || 4002;
app.listen(port, '0.0.0.0', () => {
  console.log(`🚀 Public-Link API running on port ${port}`);
  console.log(`📡 CORS enabled for GitHub Pages and localhost`);
});
