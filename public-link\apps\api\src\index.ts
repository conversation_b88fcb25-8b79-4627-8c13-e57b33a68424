import 'dotenv/config';
import express from 'express';
import fileUpload from 'express-fileupload';
import { Queue } from 'bullmq';
import Redis from 'ioredis';
import cors from 'cors';

const app = express();
app.use(cors());
app.use(express.json());
app.use(fileUpload({ useTempFiles: true, tempFileDir: '/tmp' }));

// In-memory job storage for demo (replace with Redis in production)
const jobStatuses: Record<string, any> = {};

// Redis connection (optional for demo)
let connection: Redis | null = null;
let deployQueue: Queue | null = null;
let deployUrlQueue: Queue | null = null;

try {
  connection = new Redis(process.env.REDIS_URL ?? 'redis://localhost:6379');
  deployQueue = new Queue('deploy', { connection });
  deployUrlQueue = new Queue('deploy-url', { connection });
  console.log('Redis connected - full functionality available');
} catch (error) {
  console.log('Redis not available - running in demo mode');
}

app.post('/deploy', async (req, res) => {
  if (!req.files?.project) return res.status(400).json({ error: 'project zip required' });

  if (deployQueue) {
    try {
      const job = await deployQueue.add('deploy', {
        path: (req.files.project as fileUpload.UploadedFile).tempFilePath,
        user: req.headers['x-user'] ?? 'anon'
      });
      return res.json({ jobId: job.id });
    } catch (error) {
      console.log('Redis error:', error.message);
    }
  }

  // Fallback for demo mode
  const jobId = `job_${Date.now()}`;
  jobStatuses[jobId] = { status: 'failed', error: 'Redis not available - file upload requires Redis and worker setup' };
  res.json({ jobId });
});

app.post('/deploy-url', async (req, res) => {
  console.log('Received deploy-url request:', req.body);
  const { url, platform = 'auto' } = req.body;
  if (!url) return res.status(400).json({ error: 'localhost url required' });

  // Validate platform
  const validPlatforms = ['auto', 'github-pages', 'vercel', 'railway', 'netlify'];
  if (!validPlatforms.includes(platform)) {
    return res.status(400).json({
      error: 'Invalid platform',
      validPlatforms: validPlatforms
    });
  }

  console.log(`Deploying ${url} to platform: ${platform}`);

  if (deployUrlQueue) {
    console.log('Attempting to use Redis queue...');
    try {
      // Add timeout to Redis operation
      const jobPromise = deployUrlQueue.add('deploy-url', {
        url: url,
        platform: platform,
        user: req.headers['x-user'] ?? 'anon'
      });

      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Redis timeout')), 2000)
      );

      const job = await Promise.race([jobPromise, timeoutPromise]);
      console.log('Job added to Redis queue:', job.id, 'for platform:', platform);
      return res.json({ jobId: job.id, platform });
    } catch (error) {
      console.log('Redis error:', error.message);
    }
  }

  // Fallback for direct deployment (no Redis)
  console.log('Using fallback direct deployment...');
  try {
    const jobId = `job_${Date.now()}`;
    console.log('Generated job ID:', jobId);

    // Store job status in memory
    jobStatuses[jobId] = { status: 'active', progress: 0 };

    // Process deployment directly
    setTimeout(async () => {
      try {
        console.log('Starting direct deployment for:', url, 'on platform:', platform);
        const { deployUrlDirectly } = await import('./deploy-utils.js');
        const result = await deployUrlDirectly(url, jobStatuses, jobId, platform);
        jobStatuses[jobId] = { status: 'completed', result };
        console.log('Direct deployment completed:', result);
      } catch (error) {
        console.error('Direct deployment failed:', error);
        jobStatuses[jobId] = { status: 'failed', error: error.message };
      }
    }, 500);

    console.log('Sending response with job ID:', jobId, 'for platform:', platform);
    res.json({ jobId, platform });
  } catch (error) {
    console.error('Fallback error:', error);
    res.status(500).json({ error: 'Failed to queue deployment job' });
  }
});

// New endpoint to get available platforms and their status
app.get('/platforms', (req, res) => {
  const platforms = {
    'github-pages': {
      name: 'GitHub Pages',
      description: 'Static hosting (no backend support)',
      available: !!process.env.GITHUB_TOKEN,
      supportsBackend: false,
      cost: 'Free'
    },
    'vercel': {
      name: 'Vercel',
      description: 'Full-stack hosting with serverless functions',
      available: !!process.env.VERCEL_TOKEN,
      supportsBackend: true,
      cost: 'Free tier available'
    },
    'railway': {
      name: 'Railway',
      description: 'Full-stack hosting with persistent backend',
      available: !!process.env.RAILWAY_TOKEN,
      supportsBackend: true,
      cost: 'Usage-based pricing'
    },
    'netlify': {
      name: 'Netlify',
      description: 'Static hosting with serverless functions',
      available: !!process.env.NETLIFY_TOKEN,
      supportsBackend: true,
      cost: 'Free tier available'
    }
  };

  res.json({
    platforms,
    recommendation: {
      static: 'github-pages',
      backend: process.env.RAILWAY_TOKEN ? 'railway' :
               process.env.VERCEL_TOKEN ? 'vercel' : 'github-pages'
    }
  });
});

app.get('/status/:id', async (req, res) => {
  const jobId = req.params.id;

  // Check in-memory storage first (demo mode)
  if (jobStatuses[jobId]) {
    return res.json(jobStatuses[jobId]);
  }

  // Try Redis if available
  if (deployQueue || deployUrlQueue) {
    try {
      // Check deploy queue first
      if (deployQueue) {
        const job = await deployQueue.getJob(jobId);
        if (job) {
          return res.json({ status: await job.getState(), result: job.returnvalue });
        }
      }

      // Check deploy-url queue
      if (deployUrlQueue) {
        const job = await deployUrlQueue.getJob(jobId);
        if (job) {
          return res.json({ status: await job.getState(), result: job.returnvalue });
        }
      }
    } catch (error) {
      console.log('Redis error:', error.message);
    }
  }

  res.status(404).json({ error: 'Job not found' });
});

const port = process.env.API_PORT ?? 4000;
app.listen(port, () => console.log(`API ready on ${port}`)); 