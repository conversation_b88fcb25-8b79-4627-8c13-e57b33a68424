import 'dotenv/config';
import express from 'express';
import fileUpload from 'express-fileupload';
import { Queue } from 'bullmq';
import Redis from 'ioredis';
import cors from 'cors';

const app = express();
app.use(cors());
app.use(express.json());
app.use(fileUpload({ useTempFiles: true, tempFileDir: '/tmp' }));

// In-memory job storage for demo (replace with Redis in production)
const jobStatuses: Record<string, any> = {};

// Redis connection (optional for demo)
let connection: Redis | null = null;
let deployQueue: Queue | null = null;
let deployUrlQueue: Queue | null = null;

try {
  connection = new Redis(process.env.REDIS_URL ?? 'redis://localhost:6379');
  deployQueue = new Queue('deploy', { connection });
  deployUrlQueue = new Queue('deploy-url', { connection });
  console.log('Redis connected - full functionality available');
} catch (error) {
  console.log('Redis not available - running in demo mode');
}

app.post('/deploy', async (req, res) => {
  if (!req.files?.project) return res.status(400).json({ error: 'project zip required' });

  if (deployQueue) {
    try {
      const job = await deployQueue.add('deploy', {
        path: (req.files.project as fileUpload.UploadedFile).tempFilePath,
        user: req.headers['x-user'] ?? 'anon'
      });
      return res.json({ jobId: job.id });
    } catch (error) {
      console.log('Redis error:', error.message);
    }
  }

  // Fallback for demo mode
  const jobId = `job_${Date.now()}`;
  jobStatuses[jobId] = { status: 'failed', error: 'Redis not available - file upload requires Redis and worker setup' };
  res.json({ jobId });
});

app.post('/deploy-url', async (req, res) => {
  const { url } = req.body;
  if (!url) return res.status(400).json({ error: 'localhost url required' });

  if (deployUrlQueue) {
    try {
      const job = await deployUrlQueue.add('deploy-url', {
        url: url,
        user: req.headers['x-user'] ?? 'anon'
      });
      return res.json({ jobId: job.id });
    } catch (error) {
      console.log('Redis error:', error.message);
    }
  }

  // Fallback for demo mode
  try {
    const jobId = `job_${Date.now()}`;

    // Store job status in memory (for demo)
    jobStatuses[jobId] = { status: 'active', progress: 0 };

    // Simulate processing
    setTimeout(async () => {
      try {
        jobStatuses[jobId] = { status: 'active', progress: 25 };

        // Simulate fetching the URL
        console.log(`Fetching ${url}...`);
        const response = await fetch(url);

        if (!response.ok) {
          jobStatuses[jobId] = { status: 'failed', error: `Failed to fetch ${url}: ${response.statusText}` };
          return;
        }

        jobStatuses[jobId] = { status: 'active', progress: 50 };

        const html = await response.text();
        jobStatuses[jobId] = { status: 'active', progress: 75 };

        // Simulate successful completion
        setTimeout(() => {
          jobStatuses[jobId] = {
            status: 'completed',
            result: {
              message: 'Successfully fetched and processed!',
              url: url,
              size: html.length,
              demo: 'This is a demo - Redis and GitHub integration needed for full deployment'
            }
          };
        }, 1000);

      } catch (error) {
        jobStatuses[jobId] = { status: 'failed', error: error.message };
      }
    }, 500);

    res.json({ jobId });
  } catch (error) {
    res.status(500).json({ error: 'Failed to queue deployment job' });
  }
});

app.get('/status/:id', async (req, res) => {
  const jobId = req.params.id;

  // Check in-memory storage first (demo mode)
  if (jobStatuses[jobId]) {
    return res.json(jobStatuses[jobId]);
  }

  // Try Redis if available
  if (deployQueue || deployUrlQueue) {
    try {
      // Check deploy queue first
      if (deployQueue) {
        const job = await deployQueue.getJob(jobId);
        if (job) {
          return res.json({ status: await job.getState(), result: job.returnvalue });
        }
      }

      // Check deploy-url queue
      if (deployUrlQueue) {
        const job = await deployUrlQueue.getJob(jobId);
        if (job) {
          return res.json({ status: await job.getState(), result: job.returnvalue });
        }
      }
    } catch (error) {
      console.log('Redis error:', error.message);
    }
  }

  res.status(404).json({ error: 'Job not found' });
});

const port = process.env.API_PORT ?? 4000;
app.listen(port, () => console.log(`API ready on ${port}`)); 