import 'dotenv/config';
import express from 'express';
import fileUpload from 'express-fileupload';
import { Queue } from 'bullmq';
import Redis from 'ioredis';
import cors from 'cors';

const app = express();
app.use(cors());
app.use(express.json());
app.use(fileUpload({ useTempFiles: true, tempFileDir: '/tmp' }));

const connection = new Redis(process.env.REDIS_URL ?? 'redis://localhost:6379');
export const deployQueue = new Queue('deploy', { connection });

app.post('/deploy', async (req, res) => {
  if (!req.files?.project) return res.status(400).json({ error: 'project zip required' });
  const job = await deployQueue.add('deploy', {
    path: (req.files.project as fileUpload.UploadedFile).tempFilePath,
    user: req.headers['x-user'] ?? 'anon'
  });
  res.json({ jobId: job.id });
});

app.post('/deploy-url', async (req, res) => {
  const { url } = req.body;
  if (!url) return res.status(400).json({ error: 'localhost url required' });

  try {
    const job = await deployQueue.add('deploy-url', {
      url: url,
      user: req.headers['x-user'] ?? 'anon'
    });
    res.json({ jobId: job.id });
  } catch (error) {
    res.status(500).json({ error: 'Failed to queue deployment job' });
  }
});

app.get('/status/:id', async (req, res) => {
  const job = await deployQueue.getJob(req.params.id);
  if (!job) return res.status(404).end();
  res.json({ status: await job.getState(), result: job.returnvalue });
});

const port = process.env.API_PORT ?? 4000;
app.listen(port, () => console.log(`API ready on ${port}`)); 