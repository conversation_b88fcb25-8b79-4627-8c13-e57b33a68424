const http = require('http');

const server = http.createServer((req, res) => {
  // Enable CORS
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  if (req.method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }

  if (req.url === '/') {
    res.writeHead(200, { 'Content-Type': 'text/html' });
    res.end(`
      <!DOCTYPE html>
      <html>
      <head>
        <title>Backend Test App</title>
        <style>
          body {
            font-family: Arial, sans-serif;
            text-align: center;
            padding: 50px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            margin: 0;
          }
          .container {
            max-width: 600px;
            margin: 0 auto;
            background: rgba(255,255,255,0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
          }
          button {
            margin: 10px;
            padding: 15px 30px;
            font-size: 16px;
            border: none;
            border-radius: 8px;
            background: #4CAF50;
            color: white;
            cursor: pointer;
            transition: background 0.3s;
          }
          button:hover {
            background: #45a049;
          }
          input {
            margin: 5px;
            padding: 10px;
            border: none;
            border-radius: 5px;
            font-size: 14px;
          }
          #result {
            margin: 20px 0;
            padding: 15px;
            background: rgba(255,255,255,0.2);
            border-radius: 8px;
            min-height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
          }
        </style>
      </head>
      <body>
        <div class="container">
          <h1>🚀 Backend Test Application</h1>
          <p>This app demonstrates backend functionality that will be simulated in static deployments</p>
          
          <div>
            <h3>Calculator API Test</h3>
            <input type="number" id="num1" placeholder="First number" value="10">
            <input type="number" id="num2" placeholder="Second number" value="5">
            <br>
            <button onclick="testAdd()">Add</button>
            <button onclick="testSubtract()">Subtract</button>
            <button onclick="testMultiply()">Multiply</button>
            <button onclick="testDivide()">Divide</button>
          </div>
          
          <div>
            <h3>Generic API Test</h3>
            <button onclick="testGenericAPI()">Test Generic API</button>
            <button onclick="testUserAPI()">Test User API</button>
          </div>
          
          <div id="result">Results will appear here...</div>
        </div>

        <script>
          async function makeAPICall(endpoint, data) {
            try {
              const response = await fetch(endpoint, {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
              });
              
              const result = await response.json();
              document.getElementById('result').innerHTML = 
                '<strong>✅ Success:</strong><br>' + JSON.stringify(result, null, 2);
            } catch (error) {
              document.getElementById('result').innerHTML = 
                '<strong>❌ Error:</strong><br>' + error.message;
            }
          }

          function testAdd() {
            const a = parseFloat(document.getElementById('num1').value);
            const b = parseFloat(document.getElementById('num2').value);
            makeAPICall('/api/add', { a, b });
          }

          function testSubtract() {
            const a = parseFloat(document.getElementById('num1').value);
            const b = parseFloat(document.getElementById('num2').value);
            makeAPICall('/api/subtract', { a, b });
          }

          function testMultiply() {
            const a = parseFloat(document.getElementById('num1').value);
            const b = parseFloat(document.getElementById('num2').value);
            makeAPICall('/api/multiply', { a, b });
          }

          function testDivide() {
            const a = parseFloat(document.getElementById('num1').value);
            const b = parseFloat(document.getElementById('num2').value);
            makeAPICall('/api/divide', { a, b });
          }

          function testGenericAPI() {
            makeAPICall('/api/test', { 
              message: 'Hello from frontend!',
              timestamp: new Date().toISOString()
            });
          }

          function testUserAPI() {
            makeAPICall('/api/users', { 
              action: 'get_user',
              userId: 123
            });
          }
        </script>
      </body>
      </html>
    `);
  } else if (req.url.startsWith('/api/')) {
    // Handle API endpoints
    let body = '';
    req.on('data', chunk => {
      body += chunk.toString();
    });
    
    req.on('end', () => {
      try {
        const data = JSON.parse(body || '{}');
        let result = {};

        switch (req.url) {
          case '/api/add':
            result = {
              result: (data.a || 0) + (data.b || 0),
              operation: 'addition',
              message: 'Real backend calculation'
            };
            break;
          case '/api/subtract':
            result = {
              result: (data.a || 0) - (data.b || 0),
              operation: 'subtraction',
              message: 'Real backend calculation'
            };
            break;
          case '/api/multiply':
            result = {
              result: (data.a || 0) * (data.b || 0),
              operation: 'multiplication',
              message: 'Real backend calculation'
            };
            break;
          case '/api/divide':
            result = {
              result: data.b !== 0 ? (data.a || 0) / (data.b || 1) : 'Error: Division by zero',
              operation: 'division',
              message: 'Real backend calculation'
            };
            break;
          default:
            result = {
              message: 'Real backend API response',
              endpoint: req.url,
              data: data,
              timestamp: new Date().toISOString()
            };
        }

        res.writeHead(200, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify(result));
      } catch (error) {
        res.writeHead(400, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ error: 'Invalid JSON' }));
      }
    });
  } else {
    res.writeHead(404, { 'Content-Type': 'text/plain' });
    res.end('Not Found');
  }
});

const PORT = 3001;
server.listen(PORT, () => {
  console.log(`🚀 Backend test server running on http://localhost:${PORT}`);
  console.log('This server has real backend functionality that will be simulated in static deployments');
});
