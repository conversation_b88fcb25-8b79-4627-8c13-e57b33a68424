{"version": 3, "names": ["REACT_ELEMENT_TYPE", "_createRawReactElement", "type", "props", "key", "children", "Symbol", "defaultProps", "<PERSON><PERSON><PERSON><PERSON>", "arguments", "length", "<PERSON><PERSON><PERSON><PERSON>", "Array", "i", "propName", "$$typeof", "undefined", "ref", "_owner"], "sources": ["../../src/helpers/jsx.ts"], "sourcesContent": ["/* @minVersion 7.0.0-beta.0 */\n\nvar REACT_ELEMENT_TYPE: symbol | 0xeac7;\n\ninterface Props {\n  children?: any;\n  [propName: string]: any;\n}\n\ninterface ReactElement {\n  $$typeof: typeof REACT_ELEMENT_TYPE;\n  type: any;\n  key: string | null;\n  ref: null;\n  props: Props;\n  _owner: null;\n}\n\ntype ReactElementType = any;\ntype ReactKey = string | number | bigint;\ntype ReactNode =\n  | ReactElement\n  | string\n  | number\n  | Iterable<ReactNode>\n  | boolean\n  | null\n  | undefined;\n\nexport default function _createRawReactElement(\n  type: ReactElementType,\n  props: Props,\n  key?: ReactKey,\n  children?: ReactNode[],\n): ReactElement {\n  if (!REACT_ELEMENT_TYPE) {\n    REACT_ELEMENT_TYPE =\n      (typeof Symbol === \"function\" &&\n        // \"for\" is a reserved keyword in ES3 so escaping it here for backward compatibility\n        Symbol[\"for\"] &&\n        Symbol[\"for\"](\"react.element\")) ||\n      0xeac7;\n  }\n\n  var defaultProps: Props = type && type.defaultProps;\n  var childrenLength = arguments.length - 3;\n\n  if (!props && childrenLength !== 0) {\n    // If we're going to assign props.children, we create a new object now\n    // to avoid mutating defaultProps.\n    props = { children: void 0 };\n  }\n\n  if (childrenLength === 1) {\n    props.children = children;\n  } else if (childrenLength > 1) {\n    var childArray = new Array(childrenLength);\n    for (var i = 0; i < childrenLength; i++) {\n      childArray[i] = arguments[i + 3];\n    }\n    props.children = childArray;\n  }\n\n  if (props && defaultProps) {\n    for (var propName in defaultProps) {\n      if (props[propName] === void 0) {\n        props[propName] = defaultProps[propName];\n      }\n    }\n  } else if (!props) {\n    props = defaultProps || {};\n  }\n\n  return {\n    $$typeof: REACT_ELEMENT_TYPE,\n    type: type,\n    key: key === undefined ? null : \"\" + key,\n    ref: null,\n    props: props,\n    _owner: null,\n  };\n}\n"], "mappings": ";;;;;;AAEA,IAAIA,kBAAmC;AA2BxB,SAASC,sBAAsBA,CAC5CC,IAAsB,EACtBC,KAAY,EACZC,GAAc,EACdC,QAAsB,EACR;EACd,IAAI,CAACL,kBAAkB,EAAE;IACvBA,kBAAkB,GACf,OAAOM,MAAM,KAAK,UAAU,IAE3BA,MAAM,CAAC,KAAK,CAAC,IACbA,MAAM,CAAC,KAAK,CAAC,CAAC,eAAe,CAAC,IAChC,MAAM;EACV;EAEA,IAAIC,YAAmB,GAAGL,IAAI,IAAIA,IAAI,CAACK,YAAY;EACnD,IAAIC,cAAc,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC;EAEzC,IAAI,CAACP,KAAK,IAAIK,cAAc,KAAK,CAAC,EAAE;IAGlCL,KAAK,GAAG;MAAEE,QAAQ,EAAE,KAAK;IAAE,CAAC;EAC9B;EAEA,IAAIG,cAAc,KAAK,CAAC,EAAE;IACxBL,KAAK,CAACE,QAAQ,GAAGA,QAAQ;EAC3B,CAAC,MAAM,IAAIG,cAAc,GAAG,CAAC,EAAE;IAC7B,IAAIG,UAAU,GAAG,IAAIC,KAAK,CAACJ,cAAc,CAAC;IAC1C,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,cAAc,EAAEK,CAAC,EAAE,EAAE;MACvCF,UAAU,CAACE,CAAC,CAAC,GAAGJ,SAAS,CAACI,CAAC,GAAG,CAAC,CAAC;IAClC;IACAV,KAAK,CAACE,QAAQ,GAAGM,UAAU;EAC7B;EAEA,IAAIR,KAAK,IAAII,YAAY,EAAE;IACzB,KAAK,IAAIO,QAAQ,IAAIP,YAAY,EAAE;MACjC,IAAIJ,KAAK,CAACW,QAAQ,CAAC,KAAK,KAAK,CAAC,EAAE;QAC9BX,KAAK,CAACW,QAAQ,CAAC,GAAGP,YAAY,CAACO,QAAQ,CAAC;MAC1C;IACF;EACF,CAAC,MAAM,IAAI,CAACX,KAAK,EAAE;IACjBA,KAAK,GAAGI,YAAY,IAAI,CAAC,CAAC;EAC5B;EAEA,OAAO;IACLQ,QAAQ,EAAEf,kBAAkB;IAC5BE,IAAI,EAAEA,IAAI;IACVE,GAAG,EAAEA,GAAG,KAAKY,SAAS,GAAG,IAAI,GAAG,EAAE,GAAGZ,GAAG;IACxCa,GAAG,EAAE,IAAI;IACTd,KAAK,EAAEA,KAAK;IACZe,MAAM,EAAE;EACV,CAAC;AACH", "ignoreList": []}